const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission } = require('../middleware/auth');
const pool = require('../config/database');

// Get all gateways
router.get('/gateways', authenticateToken, requirePermission('trunks.read'), async (req, res) => {
  try {
    console.log('🌐 Getting all gateways...');
    
    const [gateways] = await pool.execute(`
      SELECT 
        id,
        gwid,
        type,
        address,
        strip,
        pri_prefix,
        attrs,
        probe_mode,
        state,
        socket,
        description
      FROM dr_gateways 
      ORDER BY gwid
    `);

    // Parse attributes and enhance gateway data
    const enhancedGateways = gateways.map(gw => {
      let parsedAttrs = {};
      if (gw.attrs) {
        try {
          // Parse OpenSIPS gateway attributes format
          const attrPairs = gw.attrs.split(';');
          attrPairs.forEach(pair => {
            const [key, value] = pair.split('=');
            if (key && value) {
              parsedAttrs[key.trim()] = value.trim();
            }
          });
        } catch (error) {
          console.warn('Failed to parse gateway attributes:', gw.attrs);
        }
      }

      return {
        id: gw.id.toString(),
        gwid: gw.gwid,
        name: parsedAttrs.name || `Gateway ${gw.gwid}`,
        description: gw.description,
        host: gw.address.split(':')[0] || gw.address,
        port: parseInt(gw.address.split(':')[1]) || 5060,
        protocol: parsedAttrs.protocol || 'UDP',
        username: parsedAttrs.username,
        password: parsedAttrs.password ? '****' : undefined,
        realm: parsedAttrs.realm,
        register: parsedAttrs.register === 'true',
        registerExpires: parseInt(parsedAttrs.register_expires) || 3600,
        keepAlive: parsedAttrs.keep_alive === 'true',
        keepAliveInterval: parseInt(parsedAttrs.keep_alive_interval) || 30,
        maxChannels: parseInt(parsedAttrs.max_channels) || 100,
        currentChannels: 0, // Would need real-time data
        status: gw.state === 0 ? 'active' : gw.state === 1 ? 'inactive' : 'error',
        lastSeen: new Date().toISOString(), // Mock data
        totalCalls: 0, // Would need statistics
        successfulCalls: 0,
        failedCalls: 0,
        averageCallDuration: 0,
        codec: parsedAttrs.codecs ? parsedAttrs.codecs.split(',') : ['G.711'],
        dtmfMode: parsedAttrs.dtmf_mode || 'rfc2833',
        callerIdMode: parsedAttrs.caller_id_mode || 'pai',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    });

    res.json({
      success: true,
      data: enhancedGateways
    });

  } catch (error) {
    console.error('❌ Error getting gateways:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get gateways',
      error: error.message
    });
  }
});

// Create new gateway
router.post('/gateways', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const {
      gwid,
      name,
      description,
      host,
      port = 5060,
      protocol = 'UDP',
      username,
      password,
      realm,
      register = false,
      registerExpires = 3600,
      keepAlive = true,
      keepAliveInterval = 30,
      maxChannels = 100,
      codec = ['G.711'],
      dtmfMode = 'rfc2833',
      callerIdMode = 'pai'
    } = req.body;

    console.log('🌐 Creating new gateway:', gwid);

    if (!gwid || !host) {
      return res.status(400).json({
        success: false,
        message: 'Gateway ID and host are required'
      });
    }

    // Build attributes string
    const attrs = [
      `name=${name || gwid}`,
      `protocol=${protocol}`,
      username ? `username=${username}` : null,
      password ? `password=${password}` : null,
      realm ? `realm=${realm}` : null,
      `register=${register}`,
      `register_expires=${registerExpires}`,
      `keep_alive=${keepAlive}`,
      `keep_alive_interval=${keepAliveInterval}`,
      `max_channels=${maxChannels}`,
      `codecs=${codec.join(',')}`,
      `dtmf_mode=${dtmfMode}`,
      `caller_id_mode=${callerIdMode}`
    ].filter(Boolean).join(';');

    const address = `${host}:${port}`;

    await pool.execute(`
      INSERT INTO dr_gateways (gwid, type, address, strip, pri_prefix, attrs, probe_mode, state, description)
      VALUES (?, 0, ?, 0, NULL, ?, 0, 0, ?)
    `, [gwid, address, attrs, description]);

    console.log('✅ Gateway created successfully:', gwid);

    res.json({
      success: true,
      message: 'Gateway created successfully',
      data: { gwid }
    });

  } catch (error) {
    console.error('❌ Error creating gateway:', error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({
        success: false,
        message: 'Gateway ID already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create gateway',
      error: error.message
    });
  }
});

// Update gateway
router.put('/gateways/:gwid', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const { gwid } = req.params;
    const updateData = req.body;

    console.log('🌐 Updating gateway:', gwid);

    // Get current gateway
    const [current] = await pool.execute('SELECT * FROM dr_gateways WHERE gwid = ?', [gwid]);
    
    if (current.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Gateway not found'
      });
    }

    // Build new attributes string
    const attrs = [
      `name=${updateData.name || gwid}`,
      `protocol=${updateData.protocol || 'UDP'}`,
      updateData.username ? `username=${updateData.username}` : null,
      updateData.password ? `password=${updateData.password}` : null,
      updateData.realm ? `realm=${updateData.realm}` : null,
      `register=${updateData.register || false}`,
      `register_expires=${updateData.registerExpires || 3600}`,
      `keep_alive=${updateData.keepAlive !== false}`,
      `keep_alive_interval=${updateData.keepAliveInterval || 30}`,
      `max_channels=${updateData.maxChannels || 100}`,
      `codecs=${(updateData.codec || ['G.711']).join(',')}`,
      `dtmf_mode=${updateData.dtmfMode || 'rfc2833'}`,
      `caller_id_mode=${updateData.callerIdMode || 'pai'}`
    ].filter(Boolean).join(';');

    const address = `${updateData.host || current[0].address.split(':')[0]}:${updateData.port || 5060}`;

    await pool.execute(`
      UPDATE dr_gateways 
      SET address = ?, attrs = ?, description = ?
      WHERE gwid = ?
    `, [address, attrs, updateData.description, gwid]);

    console.log('✅ Gateway updated successfully:', gwid);

    res.json({
      success: true,
      message: 'Gateway updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating gateway:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update gateway',
      error: error.message
    });
  }
});

// Delete gateway
router.delete('/gateways/:gwid', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const { gwid } = req.params;
    console.log('🌐 Deleting gateway:', gwid);

    // Check if gateway is used in routing rules
    const [rules] = await pool.execute(`
      SELECT COUNT(*) as count FROM dr_rules 
      WHERE gwlist LIKE ? OR gwlist LIKE ? OR gwlist LIKE ? OR gwlist = ?
    `, [`%,${gwid},%`, `${gwid},%`, `%,${gwid}`, gwid]);

    if (rules[0].count > 0) {
      return res.status(409).json({
        success: false,
        message: 'Cannot delete gateway: it is used in routing rules'
      });
    }

    const [result] = await pool.execute('DELETE FROM dr_gateways WHERE gwid = ?', [gwid]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Gateway not found'
      });
    }

    console.log('✅ Gateway deleted successfully:', gwid);

    res.json({
      success: true,
      message: 'Gateway deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting gateway:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete gateway',
      error: error.message
    });
  }
});

// Test gateway connection
router.post('/gateways/:gwid/test', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const { gwid } = req.params;
    console.log('🌐 Testing gateway connection:', gwid);

    // Get gateway details
    const [gateways] = await pool.execute('SELECT * FROM dr_gateways WHERE gwid = ?', [gwid]);
    
    if (gateways.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Gateway not found'
      });
    }

    // For now, simulate a test (in real implementation, would use SIP OPTIONS)
    const testResult = {
      success: true,
      responseTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
      status: 'reachable',
      timestamp: new Date().toISOString()
    };

    console.log('✅ Gateway test completed:', gwid, testResult);

    res.json({
      success: true,
      message: 'Gateway test completed',
      data: testResult
    });

  } catch (error) {
    console.error('❌ Error testing gateway:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test gateway',
      error: error.message
    });
  }
});

// Get all routing rules
router.get('/routing-rules', authenticateToken, requirePermission('trunks.read'), async (req, res) => {
  try {
    console.log('📋 Getting all routing rules...');

    const [rules] = await pool.execute(`
      SELECT
        ruleid,
        groupid,
        prefix,
        timerec,
        priority,
        routeid,
        gwlist,
        attrs,
        description
      FROM dr_rules
      ORDER BY priority DESC, prefix
    `);

    const enhancedRules = rules.map(rule => ({
      id: rule.ruleid.toString(),
      name: rule.description || `Rule ${rule.ruleid}`,
      description: rule.description,
      priority: rule.priority,
      dialPattern: rule.prefix,
      callerPattern: null, // Not supported in basic dr_rules
      timePattern: rule.timerec,
      trunkId: rule.gwlist, // Gateway list acts as trunk
      stripDigits: 0, // Would be in gateway config
      prependDigits: '', // Would be in gateway config
      loadBalancing: 'round_robin', // Default
      failoverTrunks: rule.gwlist ? rule.gwlist.split(',').slice(1) : [],
      enabled: true, // No disabled field in dr_rules
      callCount: 0, // Would need statistics
      successRate: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));

    res.json({
      success: true,
      data: enhancedRules
    });

  } catch (error) {
    console.error('❌ Error getting routing rules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get routing rules',
      error: error.message
    });
  }
});

// Create new routing rule
router.post('/routing-rules', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const {
      name,
      description,
      priority = 0,
      dialPattern,
      timePattern,
      gwlist,
      groupid = '1'
    } = req.body;

    console.log('📋 Creating new routing rule:', name);

    if (!dialPattern || !gwlist) {
      return res.status(400).json({
        success: false,
        message: 'Dial pattern and gateway list are required'
      });
    }

    const [result] = await pool.execute(`
      INSERT INTO dr_rules (groupid, prefix, timerec, priority, gwlist, description)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [groupid, dialPattern, timePattern, priority, gwlist, description || name]);

    console.log('✅ Routing rule created successfully:', result.insertId);

    res.json({
      success: true,
      message: 'Routing rule created successfully',
      data: { id: result.insertId }
    });

  } catch (error) {
    console.error('❌ Error creating routing rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create routing rule',
      error: error.message
    });
  }
});

// Update routing rule
router.put('/routing-rules/:id', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const { id } = req.params;
    const { description, priority, dialPattern, timePattern, gwlist } = req.body;

    console.log('📋 Updating routing rule:', id);

    const [result] = await pool.execute(`
      UPDATE dr_rules
      SET prefix = ?, timerec = ?, priority = ?, gwlist = ?, description = ?
      WHERE ruleid = ?
    `, [dialPattern, timePattern, priority, gwlist, description, id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Routing rule not found'
      });
    }

    console.log('✅ Routing rule updated successfully:', id);

    res.json({
      success: true,
      message: 'Routing rule updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating routing rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update routing rule',
      error: error.message
    });
  }
});

// Delete routing rule
router.delete('/routing-rules/:id', authenticateToken, requirePermission('trunks.write'), async (req, res) => {
  try {
    const { id } = req.params;
    console.log('📋 Deleting routing rule:', id);

    const [result] = await pool.execute('DELETE FROM dr_rules WHERE ruleid = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Routing rule not found'
      });
    }

    console.log('✅ Routing rule deleted successfully:', id);

    res.json({
      success: true,
      message: 'Routing rule deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting routing rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete routing rule',
      error: error.message
    });
  }
});

// Get trunk statistics
router.get('/stats', authenticateToken, requirePermission('trunks.read'), async (req, res) => {
  try {
    console.log('📊 Getting trunk statistics...');

    // Get gateway count
    const [gatewayStats] = await pool.execute(`
      SELECT
        COUNT(*) as total_gateways,
        SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END) as active_gateways
      FROM dr_gateways
    `);

    // Get routing rules count
    const [ruleStats] = await pool.execute('SELECT COUNT(*) as total_rules FROM dr_rules');

    // Mock call statistics (would need real data from acc table)
    const stats = {
      totalTrunks: 0, // Would calculate from grouped gateways
      activeTrunks: 0,
      totalGateways: gatewayStats[0].total_gateways,
      activeGateways: gatewayStats[0].active_gateways,
      totalCalls: 0,
      activeCalls: 0,
      callsPerHour: 0,
      successRate: 0,
      averageCallDuration: 0,
      totalCapacity: 0,
      usedCapacity: 0,
      capacityUtilization: 0,
      totalCost: 0,
      costPerCall: 0,
      averagePacketLoss: 0,
      averageJitter: 0,
      averageRTT: 0,
      totalRules: ruleStats[0].total_rules
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Error getting trunk statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trunk statistics',
      error: error.message
    });
  }
});

module.exports = router;
