'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Shield, 
  User, 
  Phone, 
  Settings, 
  BarChart3, 
  Users,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { User as UserType } from '@/types/user';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: 'calls' | 'users' | 'settings' | 'monitoring' | 'recordings';
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
}

interface PermissionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: UserType;
  onSave: (userId: string, permissions: string[], role?: string) => Promise<void>;
}

const AVAILABLE_PERMISSIONS: Permission[] = [
  // Calls permissions
  { id: 'calls.read', name: 'View Calls', description: 'View call history and active calls', category: 'calls' },
  { id: 'calls.write', name: 'Manage Calls', description: 'Transfer, hold, hangup calls', category: 'calls' },
  { id: 'calls.record', name: 'Record Calls', description: 'Start/stop call recordings', category: 'calls' },
  
  // Users permissions
  { id: 'users.read', name: 'View Users', description: 'View user list and details', category: 'users' },
  { id: 'users.write', name: 'Manage Users', description: 'Create, edit, delete users', category: 'users' },
  { id: 'users.delete', name: 'Delete Users', description: 'Delete user accounts', category: 'users' },
  { id: 'users.bulk', name: 'Bulk Operations', description: 'Perform bulk user operations', category: 'users' },
  
  // Settings permissions
  { id: 'settings.read', name: 'View Settings', description: 'View system settings', category: 'settings' },
  { id: 'settings.write', name: 'Manage Settings', description: 'Modify system settings', category: 'settings' },
  
  // Monitoring permissions
  { id: 'monitoring.read', name: 'View Monitoring', description: 'View system monitoring data', category: 'monitoring' },
  { id: 'monitoring.write', name: 'Manage Monitoring', description: 'Configure monitoring settings', category: 'monitoring' },
  
  // Recordings permissions
  { id: 'recordings.read', name: 'View Recordings', description: 'View call recordings', category: 'recordings' },
  { id: 'recordings.write', name: 'Manage Recordings', description: 'Delete and manage recordings', category: 'recordings' },
  { id: 'recordings.download', name: 'Download Recordings', description: 'Download recording files', category: 'recordings' }
];

const PREDEFINED_ROLES: Role[] = [
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system access',
    permissions: AVAILABLE_PERMISSIONS.map(p => p.id)
  },
  {
    id: 'operator',
    name: 'Call Operator',
    description: 'Call management and monitoring',
    permissions: ['calls.read', 'calls.write', 'calls.record', 'users.read', 'monitoring.read', 'recordings.read']
  },
  {
    id: 'supervisor',
    name: 'Supervisor',
    description: 'User management and monitoring',
    permissions: ['calls.read', 'users.read', 'users.write', 'monitoring.read', 'recordings.read', 'recordings.download']
  },
  {
    id: 'user',
    name: 'Basic User',
    description: 'Basic call functionality',
    permissions: ['calls.read']
  }
];

export function PermissionsDialog({
  open,
  onOpenChange,
  user,
  onSave
}: PermissionsDialogProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      // Initialize with user's current permissions
      setSelectedPermissions(user.permissions || []);
      setSelectedRole(user.role || '');
    }
  }, [user]);

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId)
        ? prev.filter(p => p !== permissionId)
        : [...prev, permissionId]
    );
    setSelectedRole(''); // Clear role when manually selecting permissions
  };

  const handleRoleSelect = (roleId: string) => {
    const role = PREDEFINED_ROLES.find(r => r.id === roleId);
    if (role) {
      setSelectedRole(roleId);
      setSelectedPermissions(role.permissions);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await onSave(user.username, selectedPermissions, selectedRole);
      setSuccess('Permissions updated successfully');
      setTimeout(() => {
        onOpenChange(false);
      }, 1500);
    } catch (error: any) {
      setError(error.message || 'Failed to update permissions');
    } finally {
      setLoading(false);
    }
  };

  const getPermissionsByCategory = (category: string) => {
    return AVAILABLE_PERMISSIONS.filter(p => p.category === category);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'calls': return <Phone className="h-4 w-4" />;
      case 'users': return <Users className="h-4 w-4" />;
      case 'settings': return <Settings className="h-4 w-4" />;
      case 'monitoring': return <BarChart3 className="h-4 w-4" />;
      case 'recordings': return <User className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'calls': return 'Call Management';
      case 'users': return 'User Management';
      case 'settings': return 'System Settings';
      case 'monitoring': return 'Monitoring & Analytics';
      case 'recordings': return 'Call Recordings';
      default: return category;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>User Permissions - {user?.username}@{user?.domain}</span>
          </DialogTitle>
          <DialogDescription>
            Configure user permissions and roles
          </DialogDescription>
        </DialogHeader>

        {/* Error/Success Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="roles" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="roles">Predefined Roles</TabsTrigger>
            <TabsTrigger value="permissions">Custom Permissions</TabsTrigger>
          </TabsList>

          <TabsContent value="roles" className="space-y-4">
            <div className="space-y-4">
              <Label>Select a predefined role</Label>
              <Select value={selectedRole} onValueChange={handleRoleSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a role..." />
                </SelectTrigger>
                <SelectContent>
                  {PREDEFINED_ROLES.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name} - {role.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedRole && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">
                      {PREDEFINED_ROLES.find(r => r.id === selectedRole)?.name}
                    </CardTitle>
                    <CardDescription>
                      {PREDEFINED_ROLES.find(r => r.id === selectedRole)?.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedPermissions.map((permissionId) => {
                        const permission = AVAILABLE_PERMISSIONS.find(p => p.id === permissionId);
                        return permission ? (
                          <Badge key={permissionId} variant="secondary">
                            {permission.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <div className="space-y-6">
              {['calls', 'users', 'settings', 'monitoring', 'recordings'].map((category) => (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-sm">
                      {getCategoryIcon(category)}
                      <span>{getCategoryTitle(category)}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {getPermissionsByCategory(category).map((permission) => (
                      <div key={permission.id} className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label className="text-sm font-medium">{permission.name}</Label>
                          <p className="text-xs text-muted-foreground">{permission.description}</p>
                        </div>
                        <Switch
                          checked={selectedPermissions.includes(permission.id)}
                          onCheckedChange={() => handlePermissionToggle(permission.id)}
                        />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? 'Saving...' : 'Save Permissions'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
