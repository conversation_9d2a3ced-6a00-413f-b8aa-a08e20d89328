'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Upload, 
  Download, 
  Trash2, 
  UserX, 
  Key, 
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import { User } from '@/types/user';

interface BulkOperationsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUsers: User[];
  onBulkDelete: (userIds: string[]) => Promise<void>;
  onBulkKick: (userIds: string[]) => Promise<void>;
  onBulkPasswordReset: (userIds: string[], newPassword: string) => Promise<void>;
  onImportUsers: (users: any[]) => Promise<void>;
  onExportUsers: (users: User[]) => void;
  allUsers: User[];
}

export function BulkOperationsDialog({
  open,
  onOpenChange,
  selectedUsers,
  onBulkDelete,
  onBulkKick,
  onBulkPasswordReset,
  onImportUsers,
  onExportUsers,
  allUsers
}: BulkOperationsDialogProps) {
  const [activeTab, setActiveTab] = useState('operations');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Bulk operations state
  const [newPassword, setNewPassword] = useState('');
  
  // Import state
  const [importData, setImportData] = useState('');
  const [importFormat, setImportFormat] = useState<'json' | 'csv'>('json');

  const handleBulkOperation = async (operation: 'delete' | 'kick' | 'password') => {
    if (selectedUsers.length === 0) {
      setError('No users selected');
      return;
    }

    const userIds = selectedUsers.map(u => u.username);
    
    if (!confirm(`Are you sure you want to ${operation} ${selectedUsers.length} users?`)) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      switch (operation) {
        case 'delete':
          await onBulkDelete(userIds);
          setSuccess(`Successfully deleted ${selectedUsers.length} users`);
          break;
        case 'kick':
          await onBulkKick(userIds);
          setSuccess(`Successfully kicked ${selectedUsers.length} users`);
          break;
        case 'password':
          if (!newPassword.trim()) {
            setError('Password is required');
            return;
          }
          await onBulkPasswordReset(userIds, newPassword);
          setSuccess(`Successfully reset password for ${selectedUsers.length} users`);
          setNewPassword('');
          break;
      }
    } catch (error: any) {
      setError(error.message || `Failed to ${operation} users`);
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (!importData.trim()) {
      setError('Import data is required');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      let users: any[] = [];
      
      if (importFormat === 'json') {
        users = JSON.parse(importData);
      } else {
        // Parse CSV
        const lines = importData.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.trim());
        
        users = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim());
          const user: any = {};
          headers.forEach((header, index) => {
            user[header] = values[index];
          });
          return user;
        });
      }

      if (!Array.isArray(users) || users.length === 0) {
        setError('Invalid import data format');
        return;
      }

      await onImportUsers(users);
      setSuccess(`Successfully imported ${users.length} users`);
      setImportData('');
    } catch (error: any) {
      setError(error.message || 'Failed to import users');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    const usersToExport = selectedUsers.length > 0 ? selectedUsers : allUsers;
    onExportUsers(usersToExport);
    setSuccess(`Exported ${usersToExport.length} users`);
  };

  const generateSampleCSV = () => {
    return 'username,domain,password\nuser1,localhost,password123\nuser2,localhost,password456';
  };

  const generateSampleJSON = () => {
    return JSON.stringify([
      { username: 'user1', domain: 'localhost', password: 'password123' },
      { username: 'user2', domain: 'localhost', password: 'password456' }
    ], null, 2);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Bulk User Operations</span>
          </DialogTitle>
          <DialogDescription>
            Perform operations on multiple users or import/export user data
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="operations">Bulk Operations</TabsTrigger>
            <TabsTrigger value="import">Import Users</TabsTrigger>
            <TabsTrigger value="export">Export Users</TabsTrigger>
          </TabsList>

          {/* Error/Success Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <TabsContent value="operations" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Selected Users ({selectedUsers.length})</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedUsers.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No users selected</p>
                  ) : (
                    selectedUsers.slice(0, 10).map((user) => (
                      <Badge key={user.username} variant="secondary">
                        {user.username}@{user.domain}
                      </Badge>
                    ))
                  )}
                  {selectedUsers.length > 10 && (
                    <Badge variant="outline">+{selectedUsers.length - 10} more</Badge>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="destructive"
                  onClick={() => handleBulkOperation('delete')}
                  disabled={loading || selectedUsers.length === 0}
                  className="w-full"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected Users
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleBulkOperation('kick')}
                  disabled={loading || selectedUsers.length === 0}
                  className="w-full"
                >
                  <UserX className="h-4 w-4 mr-2" />
                  Kick Selected Users
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-password">Reset Password for Selected Users</Label>
                <div className="flex space-x-2">
                  <Input
                    id="new-password"
                    type="password"
                    placeholder="Enter new password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    disabled={loading}
                  />
                  <Button
                    onClick={() => handleBulkOperation('password')}
                    disabled={loading || selectedUsers.length === 0 || !newPassword.trim()}
                  >
                    <Key className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="import-format">Import Format</Label>
                <Select value={importFormat} onValueChange={(value: 'json' | 'csv') => setImportFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="json">JSON</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="import-data">Import Data</Label>
                <Textarea
                  id="import-data"
                  placeholder={`Paste your ${importFormat.toUpperCase()} data here...`}
                  value={importData}
                  onChange={(e) => setImportData(e.target.value)}
                  rows={8}
                  disabled={loading}
                />
              </div>

              <div className="text-sm text-muted-foreground">
                <p className="font-medium">Sample {importFormat.toUpperCase()} format:</p>
                <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-x-auto">
                  {importFormat === 'json' ? generateSampleJSON() : generateSampleCSV()}
                </pre>
              </div>

              <Button onClick={handleImport} disabled={loading || !importData.trim()} className="w-full">
                <Upload className="h-4 w-4 mr-2" />
                {loading ? 'Importing...' : 'Import Users'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Export Options</Label>
                <p className="text-sm text-muted-foreground">
                  {selectedUsers.length > 0 
                    ? `Export ${selectedUsers.length} selected users`
                    : `Export all ${allUsers.length} users`
                  }
                </p>
              </div>

              <Button onClick={handleExport} className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Export Users to JSON
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
