'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { User } from '@/types/user';
import apiClient from '@/lib/api';

interface UserManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user?: User | null;
  mode: 'create' | 'edit';
  onSuccess: () => void;
}

export function UserManagementDialog({
  open,
  onOpenChange,
  user,
  mode,
  onSuccess
}: UserManagementDialogProps) {
  const [formData, setFormData] = useState({
    username: '',
    domain: 'localhost',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Reset form when dialog opens/closes or user changes
  useEffect(() => {
    if (open) {
      if (mode === 'edit' && user) {
        setFormData({
          username: user.username,
          domain: user.domain,
          password: '',
          confirmPassword: ''
        });
      } else {
        setFormData({
          username: '',
          domain: 'localhost',
          password: '',
          confirmPassword: ''
        });
      }
      setError(null);
      setSuccess(null);
    }
  }, [open, mode, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError(null);
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      setError('Username is required');
      return false;
    }

    if (!formData.domain.trim()) {
      setError('Domain is required');
      return false;
    }

    if (mode === 'create' && !formData.password) {
      setError('Password is required');
      return false;
    }

    if (formData.password && formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    if (formData.password && formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (mode === 'create') {
        const response = await apiClient.createUser({
          username: formData.username,
          domain: formData.domain,
          password: formData.password
        });

        if (response.success) {
          setSuccess('User created successfully');
          setTimeout(() => {
            onSuccess();
            onOpenChange(false);
          }, 1500);
        } else {
          setError(response.message || 'Failed to create user');
        }
      } else {
        // Edit mode
        const updateData: any = {};
        
        if (formData.username !== user?.username) {
          updateData.newUsername = formData.username;
        }
        
        if (formData.domain !== user?.domain) {
          updateData.newDomain = formData.domain;
        }
        
        if (formData.password) {
          updateData.password = formData.password;
        }

        if (Object.keys(updateData).length === 0) {
          setError('No changes to save');
          return;
        }

        const response = await apiClient.updateUser(
          user!.username,
          updateData
        );

        if (response.success) {
          setSuccess('User updated successfully');
          setTimeout(() => {
            onSuccess();
            onOpenChange(false);
          }, 1500);
        } else {
          setError(response.message || 'Failed to update user');
        }
      }
    } catch (error: any) {
      setError(error.message || 'Operation failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create New User' : 'Edit User'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Add a new SIP user to the system'
              : 'Update user information and credentials'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Success/Error Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* Username Field */}
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              type="text"
              placeholder="Enter username"
              value={formData.username}
              onChange={handleInputChange}
              disabled={isLoading}
              required
            />
          </div>

          {/* Domain Field */}
          <div className="space-y-2">
            <Label htmlFor="domain">Domain</Label>
            <Input
              id="domain"
              name="domain"
              type="text"
              placeholder="Enter domain"
              value={formData.domain}
              onChange={handleInputChange}
              disabled={isLoading}
              required
            />
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <Label htmlFor="password">
              {mode === 'create' ? 'Password' : 'New Password (leave empty to keep current)'}
            </Label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder={mode === 'create' ? 'Enter password' : 'Enter new password'}
                value={formData.password}
                onChange={handleInputChange}
                className="pr-10"
                disabled={isLoading}
                required={mode === 'create'}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={isLoading}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          {/* Confirm Password Field */}
          {formData.password && (
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirm password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="pr-10"
                  disabled={isLoading}
                  required={!!formData.password}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === 'create' ? 'Creating...' : 'Updating...'}
                </>
              ) : (
                mode === 'create' ? 'Create User' : 'Update User'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
