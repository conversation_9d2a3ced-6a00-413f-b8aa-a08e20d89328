'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Phone, 
  PhoneOff, 
  Pause, 
  Play, 
  Users, 
  ArrowRightLeft,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Circle
} from 'lucide-react';
import { TransferDialog } from './TransferDialog';
import { ConferenceDialog } from './ConferenceDialog';

interface CallControlPanelProps {
  callId: string;
  caller: string;
  callee: string;
  status: 'active' | 'held' | 'recording';
  isRecording?: boolean;
  isMuted?: boolean;
  onHangup: (callId: string) => void;
  onTransfer: (callId: string, transferTo: string, transferType: 'blind' | 'attended') => void;
  onHold: (callId: string) => void;
  onUnhold: (callId: string) => void;
  onStartRecording: (callId: string) => void;
  onStopRecording: (callId: string) => void;
  onCreateConference: (participants: string[]) => void;
  hasPermission: (permission: string) => boolean;
}

export function CallControlPanel({
  callId,
  caller,
  callee,
  status,
  isRecording = false,
  isMuted = false,
  onHangup,
  onTransfer,
  onHold,
  onUnhold,
  onStartRecording,
  onStopRecording,
  onCreateConference,
  hasPermission
}: CallControlPanelProps) {
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);
  const [conferenceDialogOpen, setConferenceDialogOpen] = useState(false);

  const handleTransfer = async (transferTo: string, transferType: 'blind' | 'attended') => {
    await onTransfer(callId, transferTo, transferType);
  };

  const handleCreateConference = async (participants: string[]) => {
    await onCreateConference(participants);
  };

  const isHeld = status === 'held';

  return (
    <div className="flex items-center space-x-2">
      {/* Call Status Badge */}
      <Badge variant={status === 'active' ? 'default' : status === 'held' ? 'secondary' : 'outline'}>
        {status === 'active' && <Circle className="h-3 w-3 mr-1 fill-green-500 text-green-500" />}
        {status === 'held' && <Pause className="h-3 w-3 mr-1" />}
        {isRecording && <Circle className="h-3 w-3 mr-1 fill-red-500 text-red-500 animate-pulse" />}
        {status.charAt(0).toUpperCase() + status.slice(1)}
        {isRecording && ' • REC'}
      </Badge>

      {/* Control Buttons */}
      {hasPermission('calls.write') && (
        <>
          {/* Hold/Unhold */}
          <Button
            size="sm"
            variant="outline"
            onClick={() => isHeld ? onUnhold(callId) : onHold(callId)}
            title={isHeld ? 'Unhold call' : 'Hold call'}
          >
            {isHeld ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
          </Button>

          {/* Recording */}
          <Button
            size="sm"
            variant={isRecording ? "destructive" : "outline"}
            onClick={() => isRecording ? onStopRecording(callId) : onStartRecording(callId)}
            title={isRecording ? 'Stop recording' : 'Start recording'}
          >
            <Circle className={`h-4 w-4 ${isRecording ? 'animate-pulse' : ''}`} />
          </Button>

          {/* Transfer */}
          <Button
            size="sm"
            variant="outline"
            onClick={() => setTransferDialogOpen(true)}
            title="Transfer call"
          >
            <ArrowRightLeft className="h-4 w-4" />
          </Button>

          {/* Conference */}
          <Button
            size="sm"
            variant="outline"
            onClick={() => setConferenceDialogOpen(true)}
            title="Add to conference"
          >
            <Users className="h-4 w-4" />
          </Button>

          {/* Hangup */}
          <Button
            size="sm"
            variant="destructive"
            onClick={() => onHangup(callId)}
            title="Hangup call"
          >
            <PhoneOff className="h-4 w-4" />
          </Button>
        </>
      )}

      {/* Transfer Dialog */}
      <TransferDialog
        open={transferDialogOpen}
        onOpenChange={setTransferDialogOpen}
        onTransfer={handleTransfer}
        callId={callId}
        caller={caller}
        callee={callee}
      />

      {/* Conference Dialog */}
      <ConferenceDialog
        open={conferenceDialogOpen}
        onOpenChange={setConferenceDialogOpen}
        onCreateConference={handleCreateConference}
        initialParticipants={[caller, callee]}
      />
    </div>
  );
}
