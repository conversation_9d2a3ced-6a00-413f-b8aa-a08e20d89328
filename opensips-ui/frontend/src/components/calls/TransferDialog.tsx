'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Users } from 'lucide-react';

interface TransferDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTransfer: (transferTo: string, transferType: 'blind' | 'attended') => void;
  callId: string;
  caller: string;
  callee: string;
}

export function TransferDialog({ 
  open, 
  onOpenChange, 
  onTransfer, 
  callId, 
  caller, 
  callee 
}: TransferDialogProps) {
  const [transferTo, setTransferTo] = useState('');
  const [transferType, setTransferType] = useState<'blind' | 'attended'>('blind');
  const [loading, setLoading] = useState(false);

  const handleTransfer = async () => {
    if (!transferTo.trim()) return;

    setLoading(true);
    try {
      await onTransfer(transferTo, transferType);
      onOpenChange(false);
      setTransferTo('');
    } catch (error) {
      console.error('Transfer failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Phone className="h-5 w-5" />
            <span>Transfer Call</span>
          </DialogTitle>
          <DialogDescription>
            Transfer call from {caller} to {callee}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="call-id" className="text-right">
              Call ID
            </Label>
            <Input
              id="call-id"
              value={callId}
              className="col-span-3 font-mono text-sm"
              disabled
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="transfer-to" className="text-right">
              Transfer To
            </Label>
            <Input
              id="transfer-to"
              placeholder="Enter extension or number"
              value={transferTo}
              onChange={(e) => setTransferTo(e.target.value)}
              className="col-span-3"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="transfer-type" className="text-right">
              Type
            </Label>
            <Select value={transferType} onValueChange={(value: 'blind' | 'attended') => setTransferType(value)}>
              <SelectTrigger className="col-span-3">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="blind">Blind Transfer</SelectItem>
                <SelectItem value="attended">Attended Transfer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleTransfer} 
            disabled={!transferTo.trim() || loading}
          >
            {loading ? 'Transferring...' : 'Transfer Call'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
