'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Users, Plus, X } from 'lucide-react';

interface ConferenceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateConference: (participants: string[]) => void;
  initialParticipants?: string[];
}

export function ConferenceDialog({ 
  open, 
  onOpenChange, 
  onCreateConference, 
  initialParticipants = [] 
}: ConferenceDialogProps) {
  const [participants, setParticipants] = useState<string[]>(initialParticipants);
  const [newParticipant, setNewParticipant] = useState('');
  const [loading, setLoading] = useState(false);

  const addParticipant = () => {
    if (newParticipant.trim() && !participants.includes(newParticipant.trim())) {
      setParticipants([...participants, newParticipant.trim()]);
      setNewParticipant('');
    }
  };

  const removeParticipant = (participant: string) => {
    setParticipants(participants.filter(p => p !== participant));
  };

  const handleCreateConference = async () => {
    if (participants.length < 2) return;

    setLoading(true);
    try {
      await onCreateConference(participants);
      onOpenChange(false);
      setParticipants([]);
      setNewParticipant('');
    } catch (error) {
      console.error('Conference creation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addParticipant();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Create Conference</span>
          </DialogTitle>
          <DialogDescription>
            Add participants to create a conference call
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="participant" className="text-right">
              Add Participant
            </Label>
            <div className="col-span-3 flex space-x-2">
              <Input
                id="participant"
                placeholder="Enter extension or number"
                value={newParticipant}
                onChange={(e) => setNewParticipant(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1"
              />
              <Button 
                size="sm" 
                onClick={addParticipant}
                disabled={!newParticipant.trim()}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right pt-2">
              Participants ({participants.length})
            </Label>
            <div className="col-span-3 space-y-2">
              {participants.length === 0 ? (
                <p className="text-sm text-muted-foreground">No participants added yet</p>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {participants.map((participant, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                      <span>{participant}</span>
                      <button
                        onClick={() => removeParticipant(participant)}
                        className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
              {participants.length < 2 && (
                <p className="text-sm text-muted-foreground">
                  At least 2 participants required for conference
                </p>
              )}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleCreateConference} 
            disabled={participants.length < 2 || loading}
          >
            {loading ? 'Creating...' : 'Create Conference'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
