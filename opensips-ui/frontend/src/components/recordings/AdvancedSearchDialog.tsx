'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  Calendar, 
  Clock, 
  FileAudio, 
  Users, 
  Filter,
  X,
  Plus
} from 'lucide-react';

interface SearchCriteria {
  // Basic filters
  caller: string;
  callee: string;
  callId: string;
  dateRange: {
    from: string;
    to: string;
  };
  timeRange: {
    from: string;
    to: string;
  };
  
  // Duration filters
  duration: {
    min: string;
    max: string;
  };
  
  // Quality and format
  quality: string[];
  format: string[];
  
  // File size
  fileSize: {
    min: string;
    max: string;
    unit: 'KB' | 'MB' | 'GB';
  };
  
  // Transcription search
  transcriptionText: string;
  transcriptionLanguage: string;
  transcriptionConfidence: {
    min: number;
    max: number;
  };
  
  // Advanced options
  includeSystemCalls: boolean;
  onlyWithTranscription: boolean;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface AdvancedSearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSearch: (criteria: SearchCriteria) => void;
  initialCriteria?: Partial<SearchCriteria>;
}

const defaultCriteria: SearchCriteria = {
  caller: '',
  callee: '',
  callId: '',
  dateRange: { from: '', to: '' },
  timeRange: { from: '', to: '' },
  duration: { min: '', max: '' },
  quality: [],
  format: [],
  fileSize: { min: '', max: '', unit: 'MB' },
  transcriptionText: '',
  transcriptionLanguage: '',
  transcriptionConfidence: { min: 0, max: 100 },
  includeSystemCalls: false,
  onlyWithTranscription: false,
  sortBy: 'callDate',
  sortOrder: 'desc'
};

export function AdvancedSearchDialog({
  open,
  onOpenChange,
  onSearch,
  initialCriteria = {}
}: AdvancedSearchDialogProps) {
  const [criteria, setCriteria] = useState<SearchCriteria>({
    ...defaultCriteria,
    ...initialCriteria
  });

  const handleSearch = () => {
    onSearch(criteria);
    onOpenChange(false);
  };

  const handleReset = () => {
    setCriteria(defaultCriteria);
  };

  const updateCriteria = (field: string, value: any) => {
    setCriteria(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateNestedCriteria = (parent: string, field: string, value: any) => {
    setCriteria(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof SearchCriteria] as any,
        [field]: value
      }
    }));
  };

  const toggleArrayValue = (field: 'quality' | 'format', value: string) => {
    setCriteria(prev => {
      const currentArray = prev[field];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      
      return {
        ...prev,
        [field]: newArray
      };
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    
    if (criteria.caller) count++;
    if (criteria.callee) count++;
    if (criteria.callId) count++;
    if (criteria.dateRange.from || criteria.dateRange.to) count++;
    if (criteria.timeRange.from || criteria.timeRange.to) count++;
    if (criteria.duration.min || criteria.duration.max) count++;
    if (criteria.quality.length > 0) count++;
    if (criteria.format.length > 0) count++;
    if (criteria.fileSize.min || criteria.fileSize.max) count++;
    if (criteria.transcriptionText) count++;
    if (criteria.transcriptionLanguage) count++;
    if (criteria.includeSystemCalls) count++;
    if (criteria.onlyWithTranscription) count++;
    
    return count;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Advanced Search</span>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFiltersCount()} filters active
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Search recordings with advanced filters and criteria
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="transcription">Transcription</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Call Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="caller">Caller</Label>
                    <Input
                      id="caller"
                      placeholder="Enter caller number/name"
                      value={criteria.caller}
                      onChange={(e) => updateCriteria('caller', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="callee">Callee</Label>
                    <Input
                      id="callee"
                      placeholder="Enter callee number/name"
                      value={criteria.callee}
                      onChange={(e) => updateCriteria('callee', e.target.value)}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="callId">Call ID</Label>
                  <Input
                    id="callId"
                    placeholder="Enter specific call ID"
                    value={criteria.callId}
                    onChange={(e) => updateCriteria('callId', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Date & Time Range</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date-from">From Date</Label>
                    <Input
                      id="date-from"
                      type="date"
                      value={criteria.dateRange.from}
                      onChange={(e) => updateNestedCriteria('dateRange', 'from', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="date-to">To Date</Label>
                    <Input
                      id="date-to"
                      type="date"
                      value={criteria.dateRange.to}
                      onChange={(e) => updateNestedCriteria('dateRange', 'to', e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="time-from">From Time</Label>
                    <Input
                      id="time-from"
                      type="time"
                      value={criteria.timeRange.from}
                      onChange={(e) => updateNestedCriteria('timeRange', 'from', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="time-to">To Time</Label>
                    <Input
                      id="time-to"
                      type="time"
                      value={criteria.timeRange.to}
                      onChange={(e) => updateNestedCriteria('timeRange', 'to', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="media" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Duration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="duration-min">Min Duration (seconds)</Label>
                    <Input
                      id="duration-min"
                      type="number"
                      placeholder="0"
                      value={criteria.duration.min}
                      onChange={(e) => updateNestedCriteria('duration', 'min', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="duration-max">Max Duration (seconds)</Label>
                    <Input
                      id="duration-max"
                      type="number"
                      placeholder="3600"
                      value={criteria.duration.max}
                      onChange={(e) => updateNestedCriteria('duration', 'max', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Quality & Format</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Quality</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {['high', 'standard', 'low'].map((quality) => (
                      <Badge
                        key={quality}
                        variant={criteria.quality.includes(quality) ? 'default' : 'outline'}
                        className="cursor-pointer"
                        onClick={() => toggleArrayValue('quality', quality)}
                      >
                        {quality}
                        {criteria.quality.includes(quality) && (
                          <X className="h-3 w-3 ml-1" />
                        )}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <Label>Format</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {['wav', 'mp3', 'ogg'].map((format) => (
                      <Badge
                        key={format}
                        variant={criteria.format.includes(format) ? 'default' : 'outline'}
                        className="cursor-pointer"
                        onClick={() => toggleArrayValue('format', format)}
                      >
                        {format.toUpperCase()}
                        {criteria.format.includes(format) && (
                          <X className="h-3 w-3 ml-1" />
                        )}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transcription" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Transcription Search</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="transcription-text">Search in Transcription</Label>
                  <Textarea
                    id="transcription-text"
                    placeholder="Enter keywords or phrases to search in transcriptions..."
                    value={criteria.transcriptionText}
                    onChange={(e) => updateCriteria('transcriptionText', e.target.value)}
                    rows={3}
                  />
                </div>
                
                <div>
                  <Label htmlFor="transcription-language">Language</Label>
                  <Select
                    value={criteria.transcriptionLanguage}
                    onValueChange={(value) => updateCriteria('transcriptionLanguage', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any language</SelectItem>
                      <SelectItem value="en-US">English (US)</SelectItem>
                      <SelectItem value="en-GB">English (UK)</SelectItem>
                      <SelectItem value="es-ES">Spanish</SelectItem>
                      <SelectItem value="fr-FR">French</SelectItem>
                      <SelectItem value="de-DE">German</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Advanced Options</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="include-system">Include System Calls</Label>
                    <p className="text-sm text-muted-foreground">Include automated system calls</p>
                  </div>
                  <Switch
                    id="include-system"
                    checked={criteria.includeSystemCalls}
                    onCheckedChange={(checked) => updateCriteria('includeSystemCalls', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="only-transcription">Only With Transcription</Label>
                    <p className="text-sm text-muted-foreground">Show only recordings with transcriptions</p>
                  </div>
                  <Switch
                    id="only-transcription"
                    checked={criteria.onlyWithTranscription}
                    onCheckedChange={(checked) => updateCriteria('onlyWithTranscription', checked)}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sort-by">Sort By</Label>
                    <Select
                      value={criteria.sortBy}
                      onValueChange={(value) => updateCriteria('sortBy', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="callDate">Call Date</SelectItem>
                        <SelectItem value="duration">Duration</SelectItem>
                        <SelectItem value="fileSize">File Size</SelectItem>
                        <SelectItem value="caller">Caller</SelectItem>
                        <SelectItem value="callee">Callee</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="sort-order">Sort Order</Label>
                    <Select
                      value={criteria.sortOrder}
                      onValueChange={(value: 'asc' | 'desc') => updateCriteria('sortOrder', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="desc">Newest First</SelectItem>
                        <SelectItem value="asc">Oldest First</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            Reset All
          </Button>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSearch}>
            <Search className="h-4 w-4 mr-2" />
            Search ({getActiveFiltersCount()} filters)
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
