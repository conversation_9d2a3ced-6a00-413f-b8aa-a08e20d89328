'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Search, 
  Download, 
  RefreshCw, 
  Clock, 
  User, 
  Bot,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { Recording } from '@/types/api';

interface TranscriptionSegment {
  id: string;
  startTime: number;
  endTime: number;
  speaker: 'caller' | 'callee' | 'system';
  text: string;
  confidence: number;
}

interface Transcription {
  id: string;
  recordingId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  language: string;
  confidence: number;
  segments: TranscriptionSegment[];
  createdAt: string;
  completedAt?: string;
  error?: string;
}

interface TranscriptionPanelProps {
  recording: Recording;
  onSeekTo?: (time: number) => void;
}

export function TranscriptionPanel({ recording, onSeekTo }: TranscriptionPanelProps) {
  const [transcription, setTranscription] = useState<Transcription | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedSegments, setHighlightedSegments] = useState<string[]>([]);

  // Mock transcription data for demo
  const mockTranscription: Transcription = {
    id: 'trans_' + recording.id,
    recordingId: recording.id.toString(),
    status: 'completed',
    language: 'en-US',
    confidence: 0.92,
    createdAt: recording.callDate,
    completedAt: recording.callDate,
    segments: [
      {
        id: 'seg_1',
        startTime: 0,
        endTime: 3.5,
        speaker: 'caller',
        text: 'Hello, this is John calling about the support ticket.',
        confidence: 0.95
      },
      {
        id: 'seg_2',
        startTime: 3.5,
        endTime: 7.2,
        speaker: 'callee',
        text: 'Hi John, thank you for calling. Let me pull up your account.',
        confidence: 0.93
      },
      {
        id: 'seg_3',
        startTime: 7.2,
        endTime: 12.8,
        speaker: 'caller',
        text: 'I\'m having trouble with the login system. It keeps saying my password is incorrect.',
        confidence: 0.91
      },
      {
        id: 'seg_4',
        startTime: 12.8,
        endTime: 18.5,
        speaker: 'callee',
        text: 'I see the issue. It looks like your account was temporarily locked due to multiple failed attempts.',
        confidence: 0.94
      },
      {
        id: 'seg_5',
        startTime: 18.5,
        endTime: 22.1,
        speaker: 'caller',
        text: 'Oh, that makes sense. Can you unlock it for me?',
        confidence: 0.96
      },
      {
        id: 'seg_6',
        startTime: 22.1,
        endTime: 26.8,
        speaker: 'callee',
        text: 'Absolutely. I\'ve unlocked your account. You should be able to log in now.',
        confidence: 0.92
      }
    ]
  };

  // Load transcription
  const loadTranscription = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTranscription(mockTranscription);
    } catch (error) {
      console.error('Failed to load transcription:', error);
    } finally {
      setLoading(false);
    }
  };

  // Generate transcription
  const generateTranscription = async () => {
    setLoading(true);
    try {
      // Simulate transcription generation
      setTranscription({
        ...mockTranscription,
        status: 'processing'
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setTranscription(mockTranscription);
    } catch (error) {
      console.error('Failed to generate transcription:', error);
      setTranscription({
        ...mockTranscription,
        status: 'failed',
        error: 'Transcription generation failed'
      });
    } finally {
      setLoading(false);
    }
  };

  // Search in transcription
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (!term.trim() || !transcription) {
      setHighlightedSegments([]);
      return;
    }

    const matchingSegments = transcription.segments
      .filter(segment => 
        segment.text.toLowerCase().includes(term.toLowerCase())
      )
      .map(segment => segment.id);
    
    setHighlightedSegments(matchingSegments);
  };

  // Format time for display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Get speaker icon
  const getSpeakerIcon = (speaker: string) => {
    switch (speaker) {
      case 'caller':
        return <User className="h-4 w-4 text-blue-600" />;
      case 'callee':
        return <User className="h-4 w-4 text-green-600" />;
      case 'system':
        return <Bot className="h-4 w-4 text-gray-600" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  // Get speaker label
  const getSpeakerLabel = (speaker: string) => {
    switch (speaker) {
      case 'caller':
        return recording.caller;
      case 'callee':
        return recording.callee;
      case 'system':
        return 'System';
      default:
        return 'Unknown';
    }
  };

  // Export transcription
  const exportTranscription = () => {
    if (!transcription) return;

    const text = transcription.segments
      .map(segment => `[${formatTime(segment.startTime)}] ${getSpeakerLabel(segment.speaker)}: ${segment.text}`)
      .join('\n\n');

    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `transcription-${recording.callId}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  useEffect(() => {
    loadTranscription();
  }, [recording.id]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Transcription</span>
            </CardTitle>
            <CardDescription>
              AI-generated transcription of the call recording
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {transcription && (
              <>
                <Badge variant="outline">
                  {transcription.language}
                </Badge>
                <Badge variant="secondary">
                  {Math.round(transcription.confidence * 100)}% confidence
                </Badge>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status and Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {!transcription && (
              <Button onClick={generateTranscription} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Bot className="h-4 w-4 mr-2" />}
                Generate Transcription
              </Button>
            )}
            
            {transcription?.status === 'processing' && (
              <Alert>
                <Loader2 className="h-4 w-4 animate-spin" />
                <AlertDescription>
                  Transcription is being generated. This may take a few minutes...
                </AlertDescription>
              </Alert>
            )}
            
            {transcription?.status === 'failed' && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {transcription.error || 'Transcription generation failed'}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {transcription?.status === 'completed' && (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={loadTranscription} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={exportTranscription}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          )}
        </div>

        {/* Search */}
        {transcription?.status === 'completed' && (
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search in transcription..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        )}

        {/* Transcription Content */}
        {transcription?.status === 'completed' && (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {transcription.segments.map((segment) => (
              <div
                key={segment.id}
                className={`p-3 rounded-lg border transition-colors cursor-pointer hover:bg-muted/50 ${
                  highlightedSegments.includes(segment.id) 
                    ? 'bg-yellow-50 border-yellow-200' 
                    : 'bg-background'
                }`}
                onClick={() => onSeekTo?.(segment.startTime)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex items-center space-x-2 min-w-0 flex-shrink-0">
                    {getSpeakerIcon(segment.speaker)}
                    <span className="text-sm font-medium">
                      {getSpeakerLabel(segment.speaker)}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {formatTime(segment.startTime)}
                    </Badge>
                  </div>
                  <p className="text-sm flex-1 leading-relaxed">
                    {searchTerm && segment.text.toLowerCase().includes(searchTerm.toLowerCase()) ? (
                      <span
                        dangerouslySetInnerHTML={{
                          __html: segment.text.replace(
                            new RegExp(`(${searchTerm})`, 'gi'),
                            '<mark class="bg-yellow-200">$1</mark>'
                          )
                        }}
                      />
                    ) : (
                      segment.text
                    )}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    {Math.round(segment.confidence * 100)}%
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
