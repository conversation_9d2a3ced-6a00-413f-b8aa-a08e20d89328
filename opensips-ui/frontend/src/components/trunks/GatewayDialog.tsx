'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Globe, 
  Shield, 
  Settings, 
  Activity,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import { Gateway } from '@/types/trunk';

interface GatewayDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gateway?: Gateway | null;
  mode: 'create' | 'edit';
  onSave: (gatewayData: any) => Promise<void>;
}

export function GatewayDialog({
  open,
  onOpenChange,
  gateway,
  mode,
  onSave
}: GatewayDialogProps) {
  const [formData, setFormData] = useState({
    gwid: '',
    name: '',
    description: '',
    host: '',
    port: 5060,
    protocol: 'UDP',
    username: '',
    password: '',
    realm: '',
    register: false,
    registerExpires: 3600,
    keepAlive: true,
    keepAliveInterval: 30,
    maxChannels: 100,
    codec: ['G.711'],
    dtmfMode: 'rfc2833',
    callerIdMode: 'pai'
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Initialize form data
  useEffect(() => {
    if (mode === 'edit' && gateway) {
      setFormData({
        gwid: gateway.id || '',
        name: gateway.name || '',
        description: gateway.description || '',
        host: gateway.host || '',
        port: gateway.port || 5060,
        protocol: gateway.protocol || 'UDP',
        username: gateway.username || '',
        password: '', // Don't populate password for security
        realm: gateway.realm || '',
        register: gateway.register || false,
        registerExpires: gateway.registerExpires || 3600,
        keepAlive: gateway.keepAlive !== false,
        keepAliveInterval: gateway.keepAliveInterval || 30,
        maxChannels: gateway.maxChannels || 100,
        codec: gateway.codec || ['G.711'],
        dtmfMode: gateway.dtmfMode || 'rfc2833',
        callerIdMode: gateway.callerIdMode || 'pai'
      });
    } else {
      // Reset form for create mode
      setFormData({
        gwid: '',
        name: '',
        description: '',
        host: '',
        port: 5060,
        protocol: 'UDP',
        username: '',
        password: '',
        realm: '',
        register: false,
        registerExpires: 3600,
        keepAlive: true,
        keepAliveInterval: 30,
        maxChannels: 100,
        codec: ['G.711'],
        dtmfMode: 'rfc2833',
        callerIdMode: 'pai'
      });
    }
    setError(null);
    setSuccess(null);
  }, [mode, gateway, open]);

  const handleSave = async () => {
    if (!formData.gwid || !formData.host) {
      setError('Gateway ID and host are required');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await onSave(formData);
      setSuccess(`Gateway ${mode === 'create' ? 'created' : 'updated'} successfully`);
      setTimeout(() => {
        onOpenChange(false);
      }, 1500);
    } catch (error: any) {
      setError(error.message || `Failed to ${mode} gateway`);
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleCodec = (codec: string) => {
    setFormData(prev => ({
      ...prev,
      codec: prev.codec.includes(codec)
        ? prev.codec.filter(c => c !== codec)
        : [...prev.codec, codec]
    }));
  };

  const availableCodecs = ['G.711', 'G.729', 'G.722', 'GSM', 'iLBC', 'Speex'];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>{mode === 'create' ? 'Create Gateway' : 'Edit Gateway'}</span>
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Configure a new SIP gateway for outbound routing'
              : 'Modify gateway configuration and settings'
            }
          </DialogDescription>
        </DialogHeader>

        {/* Error/Success Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="connection">Connection</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Gateway Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="gwid">Gateway ID *</Label>
                    <Input
                      id="gwid"
                      placeholder="e.g., gw001"
                      value={formData.gwid}
                      onChange={(e) => updateFormData('gwid', e.target.value)}
                      disabled={mode === 'edit'}
                    />
                  </div>
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      placeholder="Gateway display name"
                      value={formData.name}
                      onChange={(e) => updateFormData('name', e.target.value)}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Gateway description..."
                    value={formData.description}
                    onChange={(e) => updateFormData('description', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="col-span-2">
                    <Label htmlFor="host">Host/IP Address *</Label>
                    <Input
                      id="host"
                      placeholder="sip.provider.com or *************"
                      value={formData.host}
                      onChange={(e) => updateFormData('host', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="port">Port</Label>
                    <Input
                      id="port"
                      type="number"
                      placeholder="5060"
                      value={formData.port}
                      onChange={(e) => updateFormData('port', parseInt(e.target.value) || 5060)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="protocol">Protocol</Label>
                  <Select value={formData.protocol} onValueChange={(value) => updateFormData('protocol', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UDP">UDP</SelectItem>
                      <SelectItem value="TCP">TCP</SelectItem>
                      <SelectItem value="TLS">TLS</SelectItem>
                      <SelectItem value="WS">WebSocket</SelectItem>
                      <SelectItem value="WSS">WebSocket Secure</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="connection" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Authentication</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      placeholder="SIP username"
                      value={formData.username}
                      onChange={(e) => updateFormData('username', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="SIP password"
                      value={formData.password}
                      onChange={(e) => updateFormData('password', e.target.value)}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="realm">Realm</Label>
                  <Input
                    id="realm"
                    placeholder="Authentication realm"
                    value={formData.realm}
                    onChange={(e) => updateFormData('realm', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Registration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="register">Enable Registration</Label>
                    <p className="text-sm text-muted-foreground">Register with the gateway</p>
                  </div>
                  <Switch
                    id="register"
                    checked={formData.register}
                    onCheckedChange={(checked) => updateFormData('register', checked)}
                  />
                </div>
                
                {formData.register && (
                  <div>
                    <Label htmlFor="registerExpires">Registration Expires (seconds)</Label>
                    <Input
                      id="registerExpires"
                      type="number"
                      placeholder="3600"
                      value={formData.registerExpires}
                      onChange={(e) => updateFormData('registerExpires', parseInt(e.target.value) || 3600)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Keep Alive</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="keepAlive">Enable Keep Alive</Label>
                    <p className="text-sm text-muted-foreground">Send periodic OPTIONS to check gateway status</p>
                  </div>
                  <Switch
                    id="keepAlive"
                    checked={formData.keepAlive}
                    onCheckedChange={(checked) => updateFormData('keepAlive', checked)}
                  />
                </div>
                
                {formData.keepAlive && (
                  <div>
                    <Label htmlFor="keepAliveInterval">Keep Alive Interval (seconds)</Label>
                    <Input
                      id="keepAliveInterval"
                      type="number"
                      placeholder="30"
                      value={formData.keepAliveInterval}
                      onChange={(e) => updateFormData('keepAliveInterval', parseInt(e.target.value) || 30)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="media" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Capacity</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label htmlFor="maxChannels">Maximum Concurrent Channels</Label>
                  <Input
                    id="maxChannels"
                    type="number"
                    placeholder="100"
                    value={formData.maxChannels}
                    onChange={(e) => updateFormData('maxChannels', parseInt(e.target.value) || 100)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Codecs</CardTitle>
                <CardDescription>Select supported audio codecs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {availableCodecs.map((codec) => (
                    <Badge
                      key={codec}
                      variant={formData.codec.includes(codec) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => toggleCodec(codec)}
                    >
                      {codec}
                      {formData.codec.includes(codec) && (
                        <X className="h-3 w-3 ml-1" />
                      )}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">DTMF & Caller ID</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="dtmfMode">DTMF Mode</Label>
                  <Select value={formData.dtmfMode} onValueChange={(value) => updateFormData('dtmfMode', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rfc2833">RFC 2833</SelectItem>
                      <SelectItem value="inband">In-band</SelectItem>
                      <SelectItem value="info">SIP INFO</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="callerIdMode">Caller ID Mode</Label>
                  <Select value={formData.callerIdMode} onValueChange={(value) => updateFormData('callerIdMode', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pai">P-Asserted-Identity</SelectItem>
                      <SelectItem value="rpid">Remote-Party-ID</SelectItem>
                      <SelectItem value="from">From Header</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? 'Saving...' : mode === 'create' ? 'Create Gateway' : 'Update Gateway'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
