// Trunk and Gateway Types

export interface Gateway {
  id: string;
  name: string;
  description?: string;
  host: string;
  port: number;
  protocol: 'UDP' | 'TCP' | 'TLS' | 'WS' | 'WSS';
  username?: string;
  password?: string;
  realm?: string;
  
  // Connection settings
  register: boolean;
  registerExpires?: number;
  keepAlive: boolean;
  keepAliveInterval?: number;
  
  // Capacity and limits
  maxChannels?: number;
  currentChannels: number;
  
  // Status
  status: 'active' | 'inactive' | 'error' | 'maintenance';
  lastSeen?: string;
  errorMessage?: string;
  
  // Statistics
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageCallDuration: number;
  
  // Advanced settings
  codec: string[];
  dtmfMode: 'rfc2833' | 'inband' | 'info';
  callerIdMode: 'pai' | 'rpid' | 'from';
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface Trunk {
  id: string;
  name: string;
  description?: string;
  type: 'sip' | 'pstn' | 'pri' | 'bri';
  
  // Gateway association
  gateways: Gateway[];
  primaryGateway: string;
  failoverEnabled: boolean;
  
  // Routing
  dialPattern: string;
  stripDigits: number;
  prependDigits: string;
  
  // Capacity
  maxConcurrentCalls: number;
  currentCalls: number;
  
  // Status
  status: 'active' | 'inactive' | 'maintenance';
  
  // Cost management
  costPerMinute?: number;
  currency?: string;
  billingIncrement?: number;
  
  // Quality settings
  codecPreference: string[];
  packetization: number;
  
  // Security
  allowedIPs: string[];
  authentication: boolean;
  encryption: boolean;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface RoutingRule {
  id: string;
  name: string;
  description?: string;
  priority: number;
  
  // Matching criteria
  dialPattern: string;
  callerPattern?: string;
  timePattern?: string;
  
  // Actions
  trunkId: string;
  stripDigits: number;
  prependDigits: string;
  
  // Advanced routing
  loadBalancing: 'round_robin' | 'least_cost' | 'least_busy' | 'random';
  failoverTrunks: string[];
  
  // Status
  enabled: boolean;
  
  // Statistics
  callCount: number;
  successRate: number;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface OutboundCall {
  id: string;
  callId: string;
  caller: string;
  callee: string;
  originalNumber: string;
  dialedNumber: string;
  
  // Routing info
  trunkId: string;
  trunkName: string;
  gatewayId: string;
  gatewayName: string;
  routingRuleId: string;
  
  // Call details
  startTime: string;
  answerTime?: string;
  endTime?: string;
  duration: number;
  status: 'ringing' | 'answered' | 'busy' | 'failed' | 'no_answer' | 'completed';
  
  // Quality metrics
  codec: string;
  packetLoss?: number;
  jitter?: number;
  rtt?: number;
  
  // Cost
  cost?: number;
  currency?: string;
  
  // Error info
  errorCode?: string;
  errorMessage?: string;
}

export interface TrunkStats {
  totalTrunks: number;
  activeTrunks: number;
  totalGateways: number;
  activeGateways: number;
  
  // Call statistics
  totalCalls: number;
  activeCalls: number;
  callsPerHour: number;
  successRate: number;
  averageCallDuration: number;
  
  // Capacity
  totalCapacity: number;
  usedCapacity: number;
  capacityUtilization: number;
  
  // Cost
  totalCost: number;
  costPerCall: number;
  
  // Quality
  averagePacketLoss: number;
  averageJitter: number;
  averageRTT: number;
}

export interface DialPlan {
  id: string;
  name: string;
  description?: string;
  
  // Pattern matching
  pattern: string;
  conditions: DialPlanCondition[];
  
  // Actions
  actions: DialPlanAction[];
  
  // Status
  enabled: boolean;
  priority: number;
  
  // Statistics
  matchCount: number;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface DialPlanCondition {
  type: 'caller_id' | 'called_number' | 'time' | 'date' | 'day_of_week' | 'custom';
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex' | 'range';
  value: string;
  negate?: boolean;
}

export interface DialPlanAction {
  type: 'route' | 'reject' | 'redirect' | 'modify_caller_id' | 'modify_called_number' | 'set_variable' | 'play_audio';
  parameters: Record<string, any>;
  order: number;
}

// API Request/Response types
export interface CreateGatewayRequest {
  name: string;
  description?: string;
  host: string;
  port: number;
  protocol: string;
  username?: string;
  password?: string;
  realm?: string;
  register: boolean;
  registerExpires?: number;
  keepAlive: boolean;
  keepAliveInterval?: number;
  maxChannels?: number;
  codec: string[];
  dtmfMode: string;
  callerIdMode: string;
}

export interface CreateTrunkRequest {
  name: string;
  description?: string;
  type: string;
  gateways: string[];
  primaryGateway: string;
  failoverEnabled: boolean;
  dialPattern: string;
  stripDigits: number;
  prependDigits: string;
  maxConcurrentCalls: number;
  costPerMinute?: number;
  currency?: string;
  billingIncrement?: number;
  codecPreference: string[];
  packetization: number;
  allowedIPs: string[];
  authentication: boolean;
  encryption: boolean;
}

export interface CreateRoutingRuleRequest {
  name: string;
  description?: string;
  priority: number;
  dialPattern: string;
  callerPattern?: string;
  timePattern?: string;
  trunkId: string;
  stripDigits: number;
  prependDigits: string;
  loadBalancing: string;
  failoverTrunks: string[];
  enabled: boolean;
}

export interface MakeCallRequest {
  caller: string;
  callee: string;
  trunkId?: string;
  callerIdOverride?: string;
  variables?: Record<string, string>;
}
