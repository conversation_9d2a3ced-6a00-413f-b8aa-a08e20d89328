// Comprehensive API Types for OpenSIPS UI

// Base API Response Structure
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Pagination
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  data: T;
  pagination: Pagination;
}

// Auth Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: User;
  permissions?: string[];
}

export interface User {
  id?: string;
  username: string;
  domain: string;
  email_address?: string;
  contact?: string;
  expires?: number;
  status: 'online' | 'offline';
  user_agent?: string;
  last_seen?: string;
  last_seen_formatted?: string;
  received?: string;
  path?: string;
  socket?: string;
  methods?: string;
  cseq?: number;
  priority?: number;
  expires_in_seconds?: number;
  ip_address?: string;
}

export interface UserStats {
  total_users: number;
  online_users: number;
  offline_users: number;
  avg_expires_in?: number;
  unique_user_agents: number;
  unique_ip_addresses: number;
}

export interface UserAgent {
  user_agent: string;
  count: number;
  online_count: number;
}

// Call Types
export interface CallRecord {
  id: number;
  callId: string;
  caller: string;
  callee: string;
  method: string;
  status: {
    code: string;
    reason: string;
  };
  time: string;
  duration: number;
  setupTime: number;
  created: string;
  isSuccessful: boolean;
}

export interface ActiveCall {
  id: number;
  callId: string;
  caller: {
    uri: string;
    tag: string;
    contact?: string;
  };
  callee: {
    uri: string;
    tag: string;
    contact?: string;
  };
  startTime: string;
  duration: number;
  state: number;
  timeout: number;
}

export interface MissedCall {
  id: number;
  callId: string;
  caller: string;
  callee: string;
  method: string;
  status: {
    code: string;
    reason: string;
  };
  time: string;
  setupTime: number;
  created: string;
}

export interface CallStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  avgDuration: number;
  totalDuration: number;
  period: string;
  hourlyStats?: HourlyCallStat[];
}

export interface HourlyCallStat {
  hour: string;
  calls: number;
  successful: number;
  failed: number;
  avgDuration: number;
}

// Recording Types
export interface Recording {
  id: string;
  callId: string;
  caller: string;
  callee: string;
  callDate: string;
  duration: number;
  fileSize: number;
  quality: 'high' | 'standard' | 'low';
  format: 'wav' | 'mp3' | 'ogg';
  filename?: string;
}

export interface RecordingStats {
  totalRecordings: number;
  totalDuration: number;
  totalSize: number;
  avgDuration: number;
  qualityDistribution: Array<{
    quality: string;
    count: number;
  }>;
  formatDistribution: Array<{
    format: string;
    count: number;
  }>;
}

// Monitoring Types
export interface SystemOverview {
  opensips: {
    status: 'running' | 'stopped' | 'unknown';
    version?: string;
    uptime?: number;
    processes?: number;
  };
  database: {
    status: 'connected' | 'disconnected' | 'unknown';
    type?: string;
    connections?: number;
  };
  system: {
    cpu_usage?: number;
    memory_usage?: number;
    disk_usage?: number;
    load_average?: number[];
  };
  calls: {
    active: number;
    total_today: number;
    successful_today: number;
    failed_today: number;
  };
  users: {
    total: number;
    online: number;
    offline: number;
  };
}

export interface CallMetric {
  timestamp: string;
  calls: number;
  successful: number;
  failed: number;
  avgDuration: number;
}

export interface TopCaller {
  caller: string;
  count: number;
  duration: number;
  avgDuration?: number;
}

export interface ErrorDistribution {
  code: string;
  reason: string;
  count: number;
  percentage: number;
}

export interface Metrics {
  period: string;
  callMetrics: CallMetric[];
  topCallers: TopCaller[];
  errorDistribution: ErrorDistribution[];
  opensipsMetrics?: any;
}

export interface SystemHealth {
  database: 'healthy' | 'unhealthy' | 'unknown';
  opensips: 'healthy' | 'unhealthy' | 'unknown';
  overall: 'healthy' | 'degraded' | 'unknown';
  timestamp: string;
  databaseError?: string;
  opensipsError?: string;
}

// API Request Parameters
export interface CallHistoryParams {
  page?: number;
  limit?: number;
  from_date?: string;
  to_date?: string;
  caller?: string;
  callee?: string;
  status?: string;
}

export interface MissedCallsParams {
  page?: number;
  limit?: number;
  from_date?: string;
  to_date?: string;
  callee?: string;
}

export interface RecordingFilters {
  caller?: string;
  callee?: string;
  from_date?: string;
  to_date?: string;
  duration_min?: string;
  duration_max?: string;
  quality?: string;
  format?: string;
}

export interface CDRParams {
  limit?: number;
  date?: string;
  caller?: string;
  callee?: string;
}

// Settings Types
export interface Settings {
  general: {
    system_name?: string;
    timezone?: string;
    language?: string;
  };
  sip: {
    domain?: string;
    port?: number;
    transport?: string;
  };
  recording: {
    enabled?: boolean;
    format?: string;
    quality?: string;
    retention_days?: number;
  };
  monitoring: {
    enabled?: boolean;
    interval?: number;
    alerts_enabled?: boolean;
  };
}

// WebSocket Event Types
export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface CallEvent extends WebSocketEvent {
  type: 'call_start' | 'call_end' | 'call_update';
  data: {
    callId: string;
    caller: string;
    callee: string;
    status?: string;
    duration?: number;
  };
}

export interface UserEvent extends WebSocketEvent {
  type: 'user_register' | 'user_unregister' | 'user_update';
  data: {
    username: string;
    domain: string;
    status: 'online' | 'offline';
  };
}
