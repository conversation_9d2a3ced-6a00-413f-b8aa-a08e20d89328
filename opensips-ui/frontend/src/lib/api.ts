import config from './config';

const API_BASE_URL = config.apiUrl;

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  getToken() {
    return this.token;
  }

  getBaseURL() {
    return this.baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}/api${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Auth endpoints
  async login(username: string, password: string) {
    return this.request<{
      success: boolean;
      token: string;
      user: any;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  async register(userData: {
    username: string;
    password: string;
    email?: string;
    domain?: string;
  }) {
    return this.request<{
      success: boolean;
      message: string;
      user: any;
    }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async verifyToken() {
    return this.request<{
      success: boolean;
      user: any;
    }>('/auth/verify');
  }

  async getCurrentUser() {
    return this.request<{
      success: boolean;
      data: { user: any };
    }>('/auth/me');
  }

  async logout() {
    return this.request<{
      success: boolean;
      message: string;
    }>('/auth/logout', {
      method: 'POST',
    });
  }

  // SIP User Management
  async getSipUsers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    domain?: string;
    status?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.domain) queryParams.append('domain', params.domain);
    if (params?.status) queryParams.append('status', params.status);

    const queryString = queryParams.toString();
    const endpoint = `/users/sip${queryString ? `?${queryString}` : ''}`;

    return this.request(endpoint);
  }

  async getSipUser(username: string, domain: string) {
    return this.request(`/users/sip/${encodeURIComponent(username)}/${encodeURIComponent(domain)}`);
  }

  async createSipUser(userData: {
    username: string;
    domain: string;
    password: string;
  }) {
    return this.request('/users/sip', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateSipUser(username: string, domain: string, updateData: {
    newUsername?: string;
    newDomain?: string;
    password?: string;
  }) {
    return this.request(`/users/sip/${encodeURIComponent(username)}/${encodeURIComponent(domain)}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  async deleteSipUser(username: string, domain: string) {
    return this.request(`/users/sip/${encodeURIComponent(username)}/${encodeURIComponent(domain)}`, {
      method: 'DELETE',
    });
  }

  async kickSipUser(username: string, domain: string) {
    return this.request(`/users/sip/${encodeURIComponent(username)}/${encodeURIComponent(domain)}/kick`, {
      method: 'POST',
    });
  }

  async bulkSipUserOperation(action: 'create' | 'delete', users: any[]) {
    return this.request('/users/sip/bulk', {
      method: 'POST',
      body: JSON.stringify({ action, users }),
    });
  }

  async getSipUserStats() {
    return this.request('/users/sip/stats');
  }

  // Users endpoints
  async getUsers() {
    return this.request<{
      success: boolean;
      users: any[];
    }>('/users');
  }

  async getUser(id: string) {
    return this.request<{
      success: boolean;
      user: any;
    }>(`/users/${id}`);
  }

  async createUser(userData: {
    username: string;
    password: string;
    email?: string;
    domain?: string;
  }) {
    return this.request<{
      success: boolean;
      message: string;
      user: any;
    }>('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(id: string, userData: {
    password?: string;
    email?: string;
  }) {
    return this.request<{
      success: boolean;
      message: string;
    }>(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: string) {
    return this.request<{
      success: boolean;
      message: string;
    }>(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  async getOnlineUsers() {
    return this.request<{
      success: boolean;
      users: any[];
    }>('/users/status/online');
  }

  // Calls endpoints
  async getCalls(params?: {
    page?: number;
    limit?: number;
    from?: string;
    to?: string;
    method?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const query = searchParams.toString();
    return this.request<{
      success: boolean;
      calls: any[];
      pagination: any;
    }>(`/calls${query ? `?${query}` : ''}`);
  }





  async getCall(id: string) {
    return this.request<{
      success: boolean;
      call: any;
    }>(`/calls/${id}`);
  }

  async getHourlyStats(date?: string) {
    const query = date ? `?date=${date}` : '';
    return this.request<{
      success: boolean;
      hourlyStats: any[];
      date: string;
    }>(`/calls/stats/hourly${query}`);
  }

  // Monitoring endpoints
  async getSystemStatus() {
    return this.request<{
      success: boolean;
      status: any;
    }>('/monitoring/status');
  }

  async getRealtimeStats() {
    return this.request<{
      success: boolean;
      stats: any;
    }>('/monitoring/stats/realtime');
  }

  async getProcesses() {
    return this.request<{
      success: boolean;
      processes: any;
    }>('/monitoring/processes');
  }

  async getMemoryUsage() {
    return this.request<{
      success: boolean;
      memory: any;
    }>('/monitoring/memory');
  }

  async reloadConfig() {
    return this.request<{
      success: boolean;
      message: string;
    }>('/monitoring/reload', {
      method: 'POST',
    });
  }

  async getDomains() {
    return this.request<{
      success: boolean;
      domains: any[];
    }>('/monitoring/domains');
  }

  async getEvents(limit?: number) {
    const query = limit ? `?limit=${limit}` : '';
    return this.request<{
      success: boolean;
      events: any[];
    }>(`/monitoring/events${query}`);
  }

  // Config endpoints
  async getConfig() {
    return this.request<{
      success: boolean;
      config: any;
    }>('/config');
  }

  // Optimized API endpoints (simplified paths)
  async getStats() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/stats');
  }

  async getUsers() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/users');
  }

  async getUsersStats() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/users/stats');
  }

  async getCalls() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/calls');
  }

  async getCDR(params?: { limit?: number; date?: string; caller?: string; callee?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.date) searchParams.append('date', params.date);
    if (params?.caller) searchParams.append('caller', params.caller);
    if (params?.callee) searchParams.append('callee', params.callee);

    const query = searchParams.toString();
    return this.request<{
      success: boolean;
      data: any;
    }>(`/cdr${query ? `?${query}` : ''}`);
  }

  async getHealth() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/health');
  }

  // Call Management APIs
  async getCallHistory(params: {
    page?: number;
    limit?: number;
    from_date?: string;
    to_date?: string;
    caller?: string;
    callee?: string;
    status?: string;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    return this.request<{
      success: boolean;
      data: any;
    }>(`/calls/history?${queryParams.toString()}`);
  }

  async getActiveCalls() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/calls/active');
  }

  async getCallStats(period: string = '24h') {
    return this.request<{
      success: boolean;
      data: any;
    }>(`/calls/stats?period=${period}`);
  }

  async getMissedCalls(params: {
    page?: number;
    limit?: number;
    from_date?: string;
    to_date?: string;
    callee?: string;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    return this.request<{
      success: boolean;
      data: any;
    }>(`/calls/missed?${queryParams.toString()}`);
  }

  async hangupCall(callId: string) {
    return this.request<{
      success: boolean;
      message?: string;
    }>(`/calls/hangup/${callId}`, {
      method: 'POST'
    });
  }

  async transferCall(callId: string, transferTo: string) {
    return this.request<{
      success: boolean;
      message?: string;
    }>(`/calls/transfer/${callId}`, {
      method: 'POST',
      body: JSON.stringify({ transferTo })
    });
  }

  // Monitoring APIs
  async getSystemOverview() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/monitoring/overview');
  }

  async getMetrics(period: string = '1h') {
    return this.request<{
      success: boolean;
      data: any;
    }>(`/monitoring/metrics?period=${period}`);
  }

  async getSystemHealth() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/monitoring/health');
  }

  async getOpenSIPSStats() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/monitoring/opensips-stats');
  }

  // Settings APIs
  async getSettings() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/settings');
  }

  async updateSettings(settings: any) {
    return this.request('/settings', {
      method: 'PUT',
      body: JSON.stringify({ settings })
    });
  }

  async getSettingsCategory(category: string) {
    return this.request(`/settings/${category}`);
  }

  async resetSettings(category?: string) {
    return this.request('/settings/reset', {
      method: 'POST',
      body: JSON.stringify({ category })
    });
  }

  async exportSettings() {
    const response = await fetch(`${this.baseURL}/settings/export/backup`, {
      headers: {
        'Authorization': `Bearer ${this.getToken()}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to export settings');
    }

    return response.blob();
  }
}

export const apiClient = new ApiClient(API_BASE_URL);

export default apiClient;
