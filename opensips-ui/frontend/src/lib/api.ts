import config from './config';
import {
  ApiResponse,
  PaginatedResponse,
  LoginRequest,
  LoginResponse,
  User,
  UserStats,
  UserAgent,
  CallRecord,
  ActiveCall,
  MissedCall,
  CallStats,
  Recording,
  RecordingStats,
  SystemOverview,
  Metrics,
  SystemHealth,
  Settings,
  SettingsResponse,
  CallHistoryParams,
  MissedCallsParams,
  RecordingFilters,
  CDRParams,
} from '../types/api';

const API_BASE_URL = config.apiUrl;

/**
 * Optimized API Client for OpenSIPS UI
 * Provides type-safe methods for all API endpoints
 */
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  // Token management
  setToken(token: string): void {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken(): void {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  getToken(): string | null {
    return this.token;
  }

  getBaseURL(): string {
    return this.baseURL;
  }

  // Core request method with improved error handling
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}/api${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add custom headers if provided
    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ 
          error: `HTTP ${response.status}: ${response.statusText}` 
        }));
        throw new Error(errorData.error || errorData.message || `HTTP ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to server');
      }
      throw error;
    }
  }

  // ==================== AUTH ENDPOINTS ====================
  
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return this.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async logout(): Promise<ApiResponse> {
    return this.request<ApiResponse>('/auth/logout', {
      method: 'POST',
    });
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/auth/profile');
  }

  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    return this.request<ApiResponse>('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({ currentPassword, newPassword }),
    });
  }

  async verifyToken(): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/auth/verify');
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/auth/me');
  }

  // ==================== USER ENDPOINTS ====================
  
  async getSipUsers(): Promise<ApiResponse<{ users: User[] }>> {
    return this.request<ApiResponse<{ users: User[] }>>('/users');
  }

  async getSipUserStats(): Promise<ApiResponse<UserStats>> {
    return this.request<ApiResponse<UserStats>>('/users/stats');
  }

  async getUserAgents(): Promise<ApiResponse<UserAgent[]>> {
    return this.request<ApiResponse<UserAgent[]>>('/users/agents');
  }

  async getOnlineUsers(): Promise<ApiResponse<User[]>> {
    return this.request<ApiResponse<User[]>>('/users/status/online');
  }

  async createUser(userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(username: string, userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>(`/users/${username}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(username: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/users/${username}`, {
      method: 'DELETE',
    });
  }

  async bulkDeleteUsers(usernames: string[]): Promise<ApiResponse> {
    return this.request<ApiResponse>('/users/bulk/delete', {
      method: 'POST',
      body: JSON.stringify({ usernames }),
    });
  }

  async bulkKickUsers(usernames: string[]): Promise<ApiResponse> {
    return this.request<ApiResponse>('/users/bulk/kick', {
      method: 'POST',
      body: JSON.stringify({ usernames }),
    });
  }

  async bulkPasswordReset(usernames: string[], newPassword: string): Promise<ApiResponse> {
    return this.request<ApiResponse>('/users/bulk/password-reset', {
      method: 'POST',
      body: JSON.stringify({ usernames, newPassword }),
    });
  }

  async importUsers(users: any[]): Promise<ApiResponse> {
    return this.request<ApiResponse>('/users/import', {
      method: 'POST',
      body: JSON.stringify({ users }),
    });
  }

  async updateUserPermissions(username: string, permissions: string[], role?: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/users/${username}/permissions`, {
      method: 'PUT',
      body: JSON.stringify({ permissions, role }),
    });
  }

  // ==================== CALL ENDPOINTS ====================
  
  async getCallHistory(params: CallHistoryParams = {}): Promise<PaginatedResponse<{ calls: CallRecord[] }>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const query = queryParams.toString();
    return this.request<PaginatedResponse<{ calls: CallRecord[] }>>(`/calls/history${query ? `?${query}` : ''}`);
  }

  async getActiveCalls(): Promise<ApiResponse<ActiveCall[]>> {
    return this.request<ApiResponse<ActiveCall[]>>('/calls/active');
  }

  async getCallStats(period: string = '24h'): Promise<ApiResponse<CallStats>> {
    return this.request<ApiResponse<CallStats>>(`/calls/stats?period=${period}`);
  }

  async getMissedCalls(params: MissedCallsParams = {}): Promise<PaginatedResponse<{ missedCalls: MissedCall[] }>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const query = queryParams.toString();
    return this.request<PaginatedResponse<{ missedCalls: MissedCall[] }>>(`/calls/missed${query ? `?${query}` : ''}`);
  }

  async getCall(id: string): Promise<ApiResponse<CallRecord>> {
    return this.request<ApiResponse<CallRecord>>(`/calls/${id}`);
  }

  async hangupCall(callId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/hangup/${callId}`, {
      method: 'POST'
    });
  }

  async transferCall(callId: string, transferTo: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/transfer/${callId}`, {
      method: 'POST',
      body: JSON.stringify({ transferTo })
    });
  }

  async holdCall(callId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/hold/${callId}`, {
      method: 'POST'
    });
  }

  async unholdCall(callId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/unhold/${callId}`, {
      method: 'POST'
    });
  }

  async startRecording(callId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/record/start/${callId}`, {
      method: 'POST'
    });
  }

  async stopRecording(callId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/record/stop/${callId}`, {
      method: 'POST'
    });
  }

  async createConference(participants: string[]): Promise<ApiResponse> {
    return this.request<ApiResponse>('/calls/conference/create', {
      method: 'POST',
      body: JSON.stringify({ participants })
    });
  }

  async addToConference(conferenceId: string, participant: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/conference/${conferenceId}/add`, {
      method: 'POST',
      body: JSON.stringify({ participant })
    });
  }

  async removeFromConference(conferenceId: string, participant: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/calls/conference/${conferenceId}/remove`, {
      method: 'POST',
      body: JSON.stringify({ participant })
    });
  }

  async getHourlyStats(date?: string): Promise<ApiResponse<{ hourlyStats: any[]; date: string }>> {
    const query = date ? `?date=${date}` : '';
    return this.request<ApiResponse<{ hourlyStats: any[]; date: string }>>(`/calls/stats/hourly${query}`);
  }

  async getCDR(params: CDRParams = {}): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const query = searchParams.toString();
    return this.request<ApiResponse<any>>(`/cdr${query ? `?${query}` : ''}`);
  }

  // ==================== RECORDING ENDPOINTS ====================

  async getRecordings(params: RecordingFilters & { page?: number; limit?: number } = {}): Promise<PaginatedResponse<{ recordings: Recording[] }>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const query = queryParams.toString();
    return this.request<PaginatedResponse<{ recordings: Recording[] }>>(`/recordings${query ? `?${query}` : ''}`);
  }

  async getRecordingStats(): Promise<ApiResponse<RecordingStats>> {
    return this.request<ApiResponse<RecordingStats>>('/recordings/stats');
  }

  async downloadRecording(recordingId: string | number): Promise<ApiResponse<{ filename: string; size: number }>> {
    return this.request<ApiResponse<{ filename: string; size: number }>>(`/recordings/download/${recordingId}`);
  }

  async streamRecording(recordingId: string | number): Promise<ApiResponse<{ duration: number; format: string }>> {
    return this.request<ApiResponse<{ duration: number; format: string }>>(`/recordings/stream/${recordingId}`);
  }

  async deleteRecording(recordingId: string | number): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/recordings/${recordingId}`, {
      method: 'DELETE',
    });
  }

  // ==================== MONITORING ENDPOINTS ====================

  async getSystemOverview(): Promise<ApiResponse<SystemOverview>> {
    return this.request<ApiResponse<SystemOverview>>('/monitoring/overview');
  }

  async getMetrics(period: string = '1h'): Promise<ApiResponse<Metrics>> {
    return this.request<ApiResponse<Metrics>>(`/monitoring/metrics?period=${period}`);
  }

  async getSystemHealth(): Promise<ApiResponse<SystemHealth>> {
    return this.request<ApiResponse<SystemHealth>>('/monitoring/health');
  }

  async getOpenSIPSStats(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/monitoring/opensips-stats');
  }

  async getSystemStatus(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/monitoring/status');
  }

  async getRealtimeStats(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/monitoring/stats/realtime');
  }

  async getProcesses(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/monitoring/processes');
  }

  async getMemoryUsage(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/monitoring/memory');
  }

  async reloadConfig(): Promise<ApiResponse> {
    return this.request<ApiResponse>('/monitoring/reload', {
      method: 'POST',
    });
  }

  async getDomains(): Promise<ApiResponse<any[]>> {
    return this.request<ApiResponse<any[]>>('/monitoring/domains');
  }

  async getEvents(limit?: number): Promise<ApiResponse<any[]>> {
    const query = limit ? `?limit=${limit}` : '';
    return this.request<ApiResponse<any[]>>(`/monitoring/events${query}`);
  }

  // ==================== SETTINGS ENDPOINTS ====================

  async getSettings(): Promise<ApiResponse<SettingsResponse>> {
    return this.request<ApiResponse<SettingsResponse>>('/settings');
  }

  async updateSettings(settings: Partial<Settings>): Promise<ApiResponse<Settings>> {
    return this.request<ApiResponse<Settings>>('/settings', {
      method: 'PUT',
      body: JSON.stringify({ settings }),
    });
  }

  // ==================== CONFIG ENDPOINTS ====================

  async getConfig(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/config');
  }

  async updateConfig(config: any): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/config', {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  }

  // ==================== LEGACY COMPATIBILITY METHODS ====================
  // These methods maintain compatibility with existing components

  async getStats(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/stats');
  }

  async getUsers(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/users');
  }

  async getUsersStats(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/users/stats');
  }

  async getCalls(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/calls');
  }

  async getHealth(): Promise<ApiResponse<any>> {
    return this.request<ApiResponse<any>>('/health');
  }
}

// Create and export the singleton instance
export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
