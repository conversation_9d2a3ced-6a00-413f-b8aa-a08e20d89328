'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import apiClient from '@/lib/api';

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'operator' | 'viewer';
  permissions: string[];
  lastLogin?: string;
  isActive: boolean;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  const router = useRouter();

  // Check for existing session on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // Verify token with backend
      const response = await apiClient.verifyToken();
      if (response.success && response.data) {
        // Convert API User to Auth User
        const authUser: User = {
          id: response.data.id || response.data.username,
          username: response.data.username,
          email: response.data.email_address || '',
          role: 'admin', // Default role, should come from API
          permissions: ['users.read', 'users.write', 'calls.read', 'recordings.read', 'monitoring.read', 'settings.read', 'settings.write'], // Default permissions
          isActive: response.data.status === 'online'
        };

        setState({
          user: authUser,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      } else {
        // Invalid token, clear it
        localStorage.removeItem('auth_token');
        setState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('auth_token');
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: 'Session verification failed'
      }));
    }
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await apiClient.login({ username, password });
      
      if (response.success && response.token && response.user) {
        // Store token
        localStorage.setItem('auth_token', response.token);

        // Convert API User to Auth User
        const authUser: User = {
          id: response.user.id || response.user.username,
          username: response.user.username,
          email: response.user.email_address || '',
          role: 'admin', // Default role, should come from API
          permissions: ['users.read', 'users.write', 'calls.read', 'recordings.read', 'monitoring.read', 'settings.read', 'settings.write'], // Default permissions
          isActive: response.user.status === 'online'
        };

        // Update state
        setState({
          user: authUser,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        return true;
      } else {
        setState(prev => ({ 
          ...prev, 
          isLoading: false,
          error: 'Login failed'
        }));
        return false;
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error.message || 'Login failed'
      }));
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    router.push('/login');
  };

  const refreshUser = async () => {
    try {
      const response = await apiClient.getCurrentUser();
      if (response.success && response.data) {
        // Convert API User to Auth User
        const authUser: User = {
          id: response.data.id || response.data.username,
          username: response.data.username,
          email: response.data.email_address || '',
          role: 'admin', // Default role, should come from API
          permissions: ['users.read', 'users.write', 'calls.read', 'recordings.read', 'monitoring.read', 'settings.read', 'settings.write'], // Default permissions
          isActive: response.data.status === 'online'
        };

        setState(prev => ({
          ...prev,
          user: authUser,
        }));
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const hasPermission = (permission: string): boolean => {
    return state.user?.permissions?.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    return state.user?.role === role;
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    refreshUser,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
