'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Phone,
  Users,
  Activity,
  RefreshCw,
  Download,
  Calendar,
  Target,
  Zap
} from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';

interface CallAnalytics {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  successRate: number;
  avgDuration: number;
  peakHour: string;
  busyDay: string;
  topCallers: Array<{
    caller: string;
    count: number;
    duration: number;
  }>;
  hourlyDistribution: Array<{
    hour: string;
    calls: number;
    success: number;
    failed: number;
  }>;
  errorDistribution: Array<{
    code: string;
    reason: string;
    count: number;
    percentage: number;
  }>;
}

export default function AnalyticsPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('24h');
  const [loading, setLoading] = useState(false);
  const [analytics, setAnalytics] = useState<CallAnalytics | null>(null);

  // Load analytics data
  const loadAnalytics = async () => {
    if (!hasPermission('calls.read')) return;
    
    setLoading(true);
    try {
      const response = await apiClient.getMetrics(timeRange);
      if (response.success && response.data) {
        // Transform data for analytics
        const data = response.data;
        const totalCalls = data.callMetrics?.reduce((sum: number, metric: any) => sum + metric.totalCalls, 0) || 0;
        const successfulCalls = data.callMetrics?.reduce((sum: number, metric: any) => sum + metric.successfulCalls, 0) || 0;
        const failedCalls = totalCalls - successfulCalls;
        
        setAnalytics({
          totalCalls,
          successfulCalls,
          failedCalls,
          successRate: totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0,
          avgDuration: data.callMetrics?.length ? data.callMetrics.reduce((sum: number, metric: any) => sum + metric.avgDuration, 0) / data.callMetrics.length : 0,
          peakHour: '14:00', // Mock data
          busyDay: 'Monday', // Mock data
          topCallers: data.topCallers?.slice(0, 5) || [],
          hourlyDistribution: data.callMetrics?.map((metric: any) => ({
            hour: new Date(metric.time).getHours().toString().padStart(2, '0') + ':00',
            calls: metric.totalCalls,
            success: metric.successfulCalls,
            failed: metric.failedCalls
          })),
          errorDistribution: data.errorDistribution?.map((error: any, index: number) => ({
            ...error,
            percentage: (error.count / failedCalls) * 100 || 0
          })) || []
        });
      }
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    return `${minutes}m ${secs}s`;
  };

  // Get trend indicator
  const getTrendIndicator = (value: number, threshold: number = 0) => {
    if (value > threshold) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else if (value < threshold) {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
    return <Activity className="h-4 w-4 text-gray-600" />;
  };

  useEffect(() => {
    loadAnalytics();
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadAnalytics, 30000);
    return () => clearInterval(interval);
  }, [timeRange]);

  return (
    <ProtectedRoute requiredPermission="calls.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Advanced Analytics</h1>
            <p className="text-muted-foreground">Detailed call analytics and insights</p>
          </div>
          <div className="flex items-center space-x-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">Last Hour</SelectItem>
                <SelectItem value="6h">Last 6h</SelectItem>
                <SelectItem value="24h">Last 24h</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={loadAnalytics} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">Loading analytics...</div>
        ) : analytics ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
                    <Phone className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{analytics.totalCalls}</div>
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      {getTrendIndicator(analytics.totalCalls, 10)}
                      <span>vs previous period</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                    <Target className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{analytics.successRate.toFixed(1)}%</div>
                    <Progress value={analytics.successRate} className="mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatDuration(analytics.avgDuration)}</div>
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      {getTrendIndicator(analytics.avgDuration, 60)}
                      <span>efficiency</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Peak Hour</CardTitle>
                    <Zap className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{analytics.peakHour}</div>
                    <p className="text-xs text-muted-foreground">
                      Busiest time today
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Call Distribution */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Call Status Distribution</CardTitle>
                    <CardDescription>Success vs Failed calls</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm">Successful</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{analytics.successfulCalls}</div>
                          <div className="text-xs text-muted-foreground">
                            {((analytics.successfulCalls / analytics.totalCalls) * 100).toFixed(1)}%
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span className="text-sm">Failed</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{analytics.failedCalls}</div>
                          <div className="text-xs text-muted-foreground">
                            {((analytics.failedCalls / analytics.totalCalls) * 100).toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Top Callers</CardTitle>
                    <CardDescription>Most active users</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analytics.topCallers.map((caller, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{index + 1}</Badge>
                            <span className="text-sm font-medium">{caller.caller}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">{caller.count} calls</div>
                            <div className="text-xs text-muted-foreground">
                              {formatDuration(caller.duration)} avg
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>System performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {analytics.successRate.toFixed(1)}%
                      </div>
                      <p className="text-sm text-muted-foreground">Success Rate</p>
                      <Progress value={analytics.successRate} className="mt-2" />
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatDuration(analytics.avgDuration)}
                      </div>
                      <p className="text-sm text-muted-foreground">Avg Call Duration</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {analytics.totalCalls}
                      </div>
                      <p className="text-sm text-muted-foreground">Total Calls</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Error Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle>Error Analysis</CardTitle>
                  <CardDescription>Most common call failures</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.errorDistribution.map((error, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="destructive">{error.code}</Badge>
                          <span className="text-sm">{error.reason}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{error.count} occurrences</div>
                          <div className="text-xs text-muted-foreground">
                            {error.percentage.toFixed(1)}% of failures
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Call Trends</CardTitle>
                  <CardDescription>Hourly call distribution</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    Chart visualization would be implemented here with recharts
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>AI Insights</CardTitle>
                  <CardDescription>Automated insights and recommendations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900">Peak Usage Pattern</h4>
                      <p className="text-sm text-blue-700">
                        Your system experiences peak usage at {analytics.peakHour}. 
                        Consider scaling resources during this time.
                      </p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-900">Success Rate</h4>
                      <p className="text-sm text-green-700">
                        Your success rate of {analytics.successRate.toFixed(1)}% is 
                        {analytics.successRate > 80 ? ' excellent' : ' below average'}. 
                        {analytics.successRate <= 80 && ' Consider investigating failed calls.'}
                      </p>
                    </div>
                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <h4 className="font-medium text-yellow-900">Call Duration</h4>
                      <p className="text-sm text-yellow-700">
                        Average call duration is {formatDuration(analytics.avgDuration)}. 
                        Monitor for unusually long calls that might indicate issues.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No analytics data available
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
