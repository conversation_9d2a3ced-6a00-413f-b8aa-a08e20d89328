'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Download, 
  Trash2, 
  Search, 
  Filter,
  RefreshCw,
  Calendar,
  Clock,
  FileAudio,
  HardDrive,
  BarChart3,
  Volume2,
  VolumeX
} from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';
import { Recording, RecordingStats } from '@/types/api';

export default function RecordingsPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('recordings');
  const [recordings, setRecordings] = useState<Recording[]>([]);
  const [stats, setStats] = useState<RecordingStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    caller: '',
    callee: '',
    from_date: '',
    to_date: '',
    duration_min: '',
    duration_max: '',
    quality: '',
    format: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Load recordings
  const loadRecordings = async () => {
    if (!hasPermission('recordings.read')) return;

    setLoading(true);
    try {
      const response = await apiClient.getRecordings({
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      });

      if (response.success) {
        setRecordings(response.data?.recordings || []);
        const paginationData = response.pagination || { page: 1, limit: 10, total: 0, totalPages: 1 };
        setPagination({
          page: paginationData.page,
          limit: paginationData.limit,
          total: paginationData.total,
          pages: paginationData.totalPages || paginationData.pages || 1
        });
      }
    } catch (error) {
      console.error('Failed to load recordings:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    if (!hasPermission('recordings.read')) return;

    try {
      const response = await apiClient.getRecordingStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Failed to load recording stats:', error);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle download
  const handleDownload = async (recording: Recording) => {
    try {
      const response = await apiClient.downloadRecording(recording.id);
      if (response.success) {
        // For demo purposes, show download info
        alert(`Download would start for: ${response.data?.filename}\nSize: ${formatFileSize(response.data?.size || 0)}`);
      }
    } catch (error) {
      console.error('Failed to download recording:', error);
    }
  };

  // Handle play/stream
  const handlePlay = async (recording: Recording) => {
    try {
      const response = await apiClient.streamRecording(recording.id);
      if (response.success) {
        // For demo purposes, show stream info
        alert(`Stream would start for: ${recording.callId}\nDuration: ${formatDuration(response.data?.duration || 0)}\nFormat: ${response.data?.format}`);
      }
    } catch (error) {
      console.error('Failed to stream recording:', error);
    }
  };

  // Handle delete
  const handleDelete = async (recording: Recording) => {
    if (!hasPermission('recordings.write')) return;

    if (!confirm(`Are you sure you want to delete recording ${recording.callId}?`)) {
      return;
    }

    try {
      const response = await apiClient.deleteRecording(recording.id);
      if (response.success) {
        loadRecordings(); // Reload list
      }
    } catch (error) {
      console.error('Failed to delete recording:', error);
    }
  };

  // Apply filters
  const applyFilters = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadRecordings();
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      caller: '',
      callee: '',
      from_date: '',
      to_date: '',
      duration_min: '',
      duration_max: '',
      quality: '',
      format: ''
    });
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  useEffect(() => {
    loadRecordings();
    loadStats();
  }, [pagination.page]);

  return (
    <ProtectedRoute requiredPermission="recordings.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Call Recordings</h1>
            <p className="text-muted-foreground">Manage and review call recordings</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={loadRecordings} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="recordings">Recordings</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
          </TabsList>

          <TabsContent value="recordings" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="mr-2 h-5 w-5" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium">Caller</label>
                    <Input
                      placeholder="Search caller..."
                      value={filters.caller}
                      onChange={(e) => setFilters(prev => ({ ...prev, caller: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Callee</label>
                    <Input
                      placeholder="Search callee..."
                      value={filters.callee}
                      onChange={(e) => setFilters(prev => ({ ...prev, callee: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">From Date</label>
                    <Input
                      type="date"
                      value={filters.from_date}
                      onChange={(e) => setFilters(prev => ({ ...prev, from_date: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">To Date</label>
                    <Input
                      type="date"
                      value={filters.to_date}
                      onChange={(e) => setFilters(prev => ({ ...prev, to_date: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Quality</label>
                    <Select value={filters.quality} onValueChange={(value) => setFilters(prev => ({ ...prev, quality: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="All qualities" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All qualities</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="standard">Standard</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Format</label>
                    <Select value={filters.format} onValueChange={(value) => setFilters(prev => ({ ...prev, format: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="All formats" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All formats</SelectItem>
                        <SelectItem value="wav">WAV</SelectItem>
                        <SelectItem value="mp3">MP3</SelectItem>
                        <SelectItem value="ogg">OGG</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Min Duration (s)</label>
                    <Input
                      type="number"
                      placeholder="0"
                      value={filters.duration_min}
                      onChange={(e) => setFilters(prev => ({ ...prev, duration_min: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Max Duration (s)</label>
                    <Input
                      type="number"
                      placeholder="999999"
                      value={filters.duration_max}
                      onChange={(e) => setFilters(prev => ({ ...prev, duration_max: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-2 mt-4">
                  <Button variant="outline" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                  <Button onClick={applyFilters}>
                    <Search className="mr-2 h-4 w-4" />
                    Apply Filters
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recordings List */}
            <Card>
              <CardHeader>
                <CardTitle>Recordings ({pagination.total})</CardTitle>
                <CardDescription>
                  Page {pagination.page} of {pagination.pages}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">Loading recordings...</div>
                ) : recordings.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No recordings found
                  </div>
                ) : (
                  <div className="space-y-4">
                    {recordings.map((recording) => (
                      <div key={recording.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <FileAudio className="h-4 w-4 text-blue-600" />
                              <span className="font-medium">{recording.caller} → {recording.callee}</span>
                              <Badge variant="outline">{recording.quality}</Badge>
                              <Badge variant="secondary">{recording.format.toUpperCase()}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                {new Date(recording.callDate).toLocaleDateString()}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {formatDuration(recording.duration)}
                              </div>
                              <div className="flex items-center">
                                <HardDrive className="h-3 w-3 mr-1" />
                                {formatFileSize(recording.fileSize)}
                              </div>
                              <div className="text-xs">
                                ID: {recording.callId}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handlePlay(recording)}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDownload(recording)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            {hasPermission('recordings.write') && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDelete(recording)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="flex justify-center space-x-2 mt-6">
                    <Button
                      variant="outline"
                      disabled={pagination.page === 1}
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    >
                      Previous
                    </Button>
                    <span className="flex items-center px-4">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                    <Button
                      variant="outline"
                      disabled={pagination.page === pagination.pages}
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="statistics" className="space-y-4">
            {stats ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Recordings</CardTitle>
                    <FileAudio className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalRecordings}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Duration</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatDuration(stats.totalDuration)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Size</CardTitle>
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatFileSize(stats.totalSize)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatDuration(stats.avgDuration)}</div>
                  </CardContent>
                </Card>

                {/* Quality Distribution */}
                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>Quality Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stats.qualityDistribution.map((item) => (
                        <div key={item.quality} className="flex items-center justify-between">
                          <span className="capitalize">{item.quality}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{
                                  width: `${(item.count / stats.totalRecordings) * 100}%`
                                }}
                              ></div>
                            </div>
                            <span className="text-sm text-muted-foreground">{item.count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Format Distribution */}
                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>Format Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stats.formatDistribution.map((item) => (
                        <div key={item.format} className="flex items-center justify-between">
                          <span className="uppercase">{item.format}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{
                                  width: `${(item.count / stats.totalRecordings) * 100}%`
                                }}
                              ></div>
                            </div>
                            <span className="text-sm text-muted-foreground">{item.count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="text-center py-8">Loading statistics...</div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
}
