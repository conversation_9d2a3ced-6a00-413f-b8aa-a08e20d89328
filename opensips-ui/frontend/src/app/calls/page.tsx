'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Phone, PhoneOff, Clock, Users, TrendingUp, Search, Filter, RefreshCw } from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';
import { <PERSON><PERSON><PERSON>ord, ActiveCall, <PERSON><PERSON>tats, MissedCall } from '@/types/call';
import { CallControlPanel } from '@/components/calls/CallControlPanel';

export default function CallsPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('history');
  const [loading, setLoading] = useState(false);

  // Call History State
  const [callHistory, setCallHistory] = useState<CallRecord[]>([]);
  const [historyPagination, setHistoryPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Active Calls State
  const [activeCalls, setActiveCalls] = useState<ActiveCall[]>([]);

  // Call Stats State
  const [callStats, setCallStats] = useState<CallStats | null>(null);
  const [statsPeriod, setStatsPeriod] = useState('24h');

  // Missed Calls State
  const [missedCalls, setMissedCalls] = useState<MissedCall[]>([]);
  const [missedPagination, setMissedPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Filters
  const [filters, setFilters] = useState({
    caller: '',
    callee: '',
    status: 'all',
    from_date: '',
    to_date: ''
  });

  // Load call history
  const loadCallHistory = async (page = 1) => {
    if (!hasPermission('calls.read')) return;

    setLoading(true);
    try {
      const response = await apiClient.getCallHistory({
        page,
        limit: historyPagination.limit,
        caller: filters.caller,
        callee: filters.callee,
        status: filters.status === 'all' ? '' : filters.status,
        from_date: filters.from_date,
        to_date: filters.to_date
      });

      if (response.success) {
        setCallHistory(response.data?.calls || []);
        setHistoryPagination(response.pagination || { page: 1, limit: 10, total: 0, totalPages: 1 });
      }
    } catch (error) {
      console.error('Failed to load call history:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load active calls
  const loadActiveCalls = async () => {
    if (!hasPermission('calls.read')) return;

    try {
      const response = await apiClient.getActiveCalls();
      if (response.success) {
        setActiveCalls(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load active calls:', error);
    }
  };

  // Load call stats
  const loadCallStats = async () => {
    if (!hasPermission('calls.read')) return;

    try {
      const response = await apiClient.getCallStats(statsPeriod);
      if (response.success && response.data) {
        // Transform data to match CallStats interface
        const data = response.data;
        const transformedStats = {
          ...data,
          successRate: data.totalCalls > 0 ? Math.round((data.successfulCalls / data.totalCalls) * 100) : 0,
          activeCalls: activeCalls.length, // Use current active calls count
          averageDuration: data.avgDuration || 0
        };
        setCallStats(transformedStats);
      }
    } catch (error) {
      console.error('Failed to load call stats:', error);
    }
  };

  // Load missed calls
  const loadMissedCalls = async (page = 1) => {
    if (!hasPermission('calls.read')) return;

    setLoading(true);
    try {
      const response = await apiClient.getMissedCalls({
        page,
        limit: missedPagination.limit,
        callee: filters.callee,
        from_date: filters.from_date,
        to_date: filters.to_date
      });

      if (response.success) {
        setMissedCalls(response.data?.missedCalls || []);
        setMissedPagination(response.pagination || { page: 1, limit: 10, total: 0, totalPages: 1 });
      }
    } catch (error) {
      console.error('Failed to load missed calls:', error);
    } finally {
      setLoading(false);
    }
  };

  // Hangup call
  const handleHangup = async (callId: string) => {
    if (!hasPermission('calls.write')) return;

    if (!confirm('Are you sure you want to hangup this call?')) return;

    try {
      const response = await apiClient.hangupCall(callId);
      if (response.success) {
        alert('Call hung up successfully');
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to hangup call');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to hangup call');
    }
  };

  // Transfer call
  const handleTransfer = async (callId: string, transferTo: string, transferType: 'blind' | 'attended') => {
    if (!hasPermission('calls.write')) return;

    try {
      const response = await apiClient.transferCall(callId, transferTo);
      if (response.success) {
        alert(`Call transferred successfully (${transferType})`);
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to transfer call');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to transfer call');
    }
  };

  // Hold call
  const handleHold = async (callId: string) => {
    if (!hasPermission('calls.write')) return;

    try {
      const response = await apiClient.holdCall(callId);
      if (response.success) {
        alert('Call put on hold');
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to hold call');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to hold call');
    }
  };

  // Unhold call
  const handleUnhold = async (callId: string) => {
    if (!hasPermission('calls.write')) return;

    try {
      const response = await apiClient.unholdCall(callId);
      if (response.success) {
        alert('Call resumed');
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to unhold call');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to unhold call');
    }
  };

  // Start recording
  const handleStartRecording = async (callId: string) => {
    if (!hasPermission('calls.write')) return;

    try {
      const response = await apiClient.startRecording(callId);
      if (response.success) {
        alert('Recording started');
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to start recording');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to start recording');
    }
  };

  // Stop recording
  const handleStopRecording = async (callId: string) => {
    if (!hasPermission('calls.write')) return;

    try {
      const response = await apiClient.stopRecording(callId);
      if (response.success) {
        alert('Recording stopped');
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to stop recording');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to stop recording');
    }
  };

  // Create conference
  const handleCreateConference = async (participants: string[]) => {
    if (!hasPermission('calls.write')) return;

    try {
      const response = await apiClient.createConference(participants);
      if (response.success) {
        alert('Conference created successfully');
        loadActiveCalls();
      } else {
        alert(response.message || 'Failed to create conference');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to create conference');
    }
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Get status badge color
  const getStatusColor = (code: string) => {
    if (code === '200') return 'bg-green-500';
    if (code.startsWith('4')) return 'bg-red-500';
    if (code.startsWith('5')) return 'bg-red-600';
    return 'bg-gray-500';
  };

  useEffect(() => {
    if (activeTab === 'history') {
      loadCallHistory();
    } else if (activeTab === 'active') {
      loadActiveCalls();
      // Auto-refresh active calls every 5 seconds
      const interval = setInterval(loadActiveCalls, 5000);
      return () => clearInterval(interval);
    } else if (activeTab === 'stats') {
      loadCallStats();
    } else if (activeTab === 'missed') {
      loadMissedCalls();
    }
  }, [activeTab, statsPeriod]);

  useEffect(() => {
    if (activeTab === 'history') {
      loadCallHistory(1);
    } else if (activeTab === 'missed') {
      loadMissedCalls(1);
    }
  }, [filters]);

  return (
    <ProtectedRoute requiredPermission="calls.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Call Management</h1>
            <p className="text-muted-foreground">Monitor and manage calls</p>
          </div>
          <Button onClick={() => {
            if (activeTab === 'history') loadCallHistory();
            else if (activeTab === 'active') loadActiveCalls();
            else if (activeTab === 'stats') loadCallStats();
            else if (activeTab === 'missed') loadMissedCalls();
          }}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="history">Call History</TabsTrigger>
            <TabsTrigger value="active">Active Calls</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="missed">Missed Calls</TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Call History</CardTitle>
                <CardDescription>View all call records with filtering options</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                  <Input
                    placeholder="Caller"
                    value={filters.caller}
                    onChange={(e) => setFilters(prev => ({ ...prev, caller: e.target.value }))}
                  />
                  <Input
                    placeholder="Callee"
                    value={filters.callee}
                    onChange={(e) => setFilters(prev => ({ ...prev, callee: e.target.value }))}
                  />
                  <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value === 'all' ? '' : value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="200">Success (200)</SelectItem>
                      <SelectItem value="404">Not Found (404)</SelectItem>
                      <SelectItem value="486">Busy (486)</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    type="datetime-local"
                    placeholder="From Date"
                    value={filters.from_date}
                    onChange={(e) => setFilters(prev => ({ ...prev, from_date: e.target.value }))}
                  />
                  <Input
                    type="datetime-local"
                    placeholder="To Date"
                    value={filters.to_date}
                    onChange={(e) => setFilters(prev => ({ ...prev, to_date: e.target.value }))}
                  />
                </div>

                {/* Call History Table */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Time</TableHead>
                        <TableHead>Caller</TableHead>
                        <TableHead>Callee</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Call ID</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center">Loading...</TableCell>
                        </TableRow>
                      ) : callHistory.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center">No call records found</TableCell>
                        </TableRow>
                      ) : (
                        callHistory.map((call) => (
                          <TableRow key={call.id}>
                            <TableCell>{formatDate(call.time)}</TableCell>
                            <TableCell>{call.caller}</TableCell>
                            <TableCell>{call.callee}</TableCell>
                            <TableCell>{formatDuration(call.duration)}</TableCell>
                            <TableCell>
                              <Badge className={`${getStatusColor(call.status.code)} text-white`}>
                                {call.status.code} {call.status.reason}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-mono text-sm">{call.callId}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {historyPagination.totalPages > 1 && (
                  <div className="flex justify-between items-center mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing {((historyPagination.page - 1) * historyPagination.limit) + 1} to{' '}
                      {Math.min(historyPagination.page * historyPagination.limit, historyPagination.total)} of{' '}
                      {historyPagination.total} results
                    </p>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadCallHistory(historyPagination.page - 1)}
                        disabled={historyPagination.page <= 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadCallHistory(historyPagination.page + 1)}
                        disabled={historyPagination.page >= historyPagination.totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Active Calls</CardTitle>
                <CardDescription>Monitor and control ongoing calls</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Start Time</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Caller</TableHead>
                        <TableHead>Callee</TableHead>
                        <TableHead>Call ID</TableHead>
                        <TableHead>Call Controls</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {activeCalls.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center">
                            No active calls
                          </TableCell>
                        </TableRow>
                      ) : (
                        activeCalls.map((call) => (
                          <TableRow key={call.id}>
                            <TableCell>{formatDate(call.startTime)}</TableCell>
                            <TableCell>{formatDuration(call.duration)}</TableCell>
                            <TableCell>{call.caller.uri}</TableCell>
                            <TableCell>{call.callee.uri}</TableCell>
                            <TableCell className="font-mono text-sm">{call.callId}</TableCell>
                            <TableCell>
                              <CallControlPanel
                                callId={call.callId}
                                caller={call.caller.uri}
                                callee={call.callee.uri}
                                status="active"
                                isRecording={false}
                                onHangup={handleHangup}
                                onTransfer={handleTransfer}
                                onHold={handleHold}
                                onUnhold={handleUnhold}
                                onStartRecording={handleStartRecording}
                                onStopRecording={handleStopRecording}
                                onCreateConference={handleCreateConference}
                                hasPermission={hasPermission}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Call Statistics</h3>
              <Select value={statsPeriod} onValueChange={setStatsPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="24h">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {callStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
                    <Phone className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{callStats.totalCalls}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{callStats.successRate}%</div>
                    <p className="text-xs text-muted-foreground">
                      {callStats.successfulCalls} successful
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{callStats.activeCalls}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatDuration(callStats.averageDuration)}</div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="missed" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Missed Calls</CardTitle>
                <CardDescription>View missed call records</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <Input
                    placeholder="Callee"
                    value={filters.callee}
                    onChange={(e) => setFilters(prev => ({ ...prev, callee: e.target.value }))}
                  />
                  <Input
                    type="datetime-local"
                    placeholder="From Date"
                    value={filters.from_date}
                    onChange={(e) => setFilters(prev => ({ ...prev, from_date: e.target.value }))}
                  />
                  <Input
                    type="datetime-local"
                    placeholder="To Date"
                    value={filters.to_date}
                    onChange={(e) => setFilters(prev => ({ ...prev, to_date: e.target.value }))}
                  />
                </div>

                {/* Missed Calls Table */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Time</TableHead>
                        <TableHead>Caller</TableHead>
                        <TableHead>Callee</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Call ID</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center">Loading...</TableCell>
                        </TableRow>
                      ) : missedCalls.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center">No missed calls found</TableCell>
                        </TableRow>
                      ) : (
                        missedCalls.map((call) => (
                          <TableRow key={call.id}>
                            <TableCell>{formatDate(call.time)}</TableCell>
                            <TableCell>{call.caller}</TableCell>
                            <TableCell>{call.callee}</TableCell>
                            <TableCell>
                              <Badge className={`${getStatusColor(call.status.code)} text-white`}>
                                {call.status.code} {call.status.reason}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-mono text-sm">{call.callId}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {missedPagination.totalPages > 1 && (
                  <div className="flex justify-between items-center mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing {((missedPagination.page - 1) * missedPagination.limit) + 1} to{' '}
                      {Math.min(missedPagination.page * missedPagination.limit, missedPagination.total)} of{' '}
                      {missedPagination.total} results
                    </p>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadMissedCalls(missedPagination.page - 1)}
                        disabled={missedPagination.page <= 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadMissedCalls(missedPagination.page + 1)}
                        disabled={missedPagination.page >= missedPagination.totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
}
