'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// import { toast } from '@/components/ui/use-toast';
import { Users, UserPlus, Search, Filter, RefreshCw, Grid, List, BarChart3, Plus, Settings, Download, Upload } from 'lucide-react';
import { useSocket, useRealtimeUsers } from '@/hooks/useSocket';
import socketService from '@/lib/socket';
import apiClient from '@/lib/api';
import { UserCard } from '@/components/users/UserCard';
import { UserCardSkeleton } from '@/components/users/UserCardSkeleton';
import { UserStats } from '@/components/users/UserStats';
import { UserManagementDialog } from '@/components/users/UserManagementDialog';
import { UserActions } from '@/components/users/UserActions';
import { BulkOperationsDialog } from '@/components/users/BulkOperationsDialog';
import { PermissionsDialog } from '@/components/users/PermissionsDialog';
import { User, UserStats as ApiUserStats } from '@/types/api';
import { UserFilter } from '@/types/user';
import config from '@/lib/config';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';

function UsersPageContent() {
  const { hasPermission } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<ApiUserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filter, setFilter] = useState<UserFilter>({
    status: 'all',
    search: '',
    domain: 'all',
    userAgent: 'all'
  });
  const [refreshing, setRefreshing] = useState(false);

  // User management state
  const [showUserDialog, setShowUserDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [showBulkDialog, setShowBulkDialog] = useState(false);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [permissionsUser, setPermissionsUser] = useState<User | null>(null);

  const fetchUsers = async () => {
    try {
      setError(null);
      console.log('👥 Fetching users from API...');
      config.debug(); // Debug configuration

      const result = await apiClient.getSipUsers();
      console.log('👥 Users API Result:', result);

      if (result.success && result.data && result.data.users) {
        console.log(`✅ Loaded ${result.data.users.length} users from API`);
        console.log('👥 Sample user data:', result.data.users[0]);
        setUsers(result.data.users);
        setError(null);
      } else {
        console.log('⚠️ Users API failed:', result);
        setError('Không thể tải danh sách người dùng');
        setUsers([]);
      }
    } catch (error) {
      console.error('❌ Failed to fetch users:', error);
      setError('Lỗi kết nối đến server');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      console.log('📊 Fetching user stats from API...');

      const result = await apiClient.getSipUserStats();
      console.log('📊 Stats API Result:', result);

      if (result.success && result.data) {
        console.log('✅ Loaded user stats from API');
        setStats(result.data);
        setStatsLoading(false);
        return;
      } else {
        console.log('⚠️ Stats API failed:', result);
        setStatsLoading(false);
      }
    } catch (error) {
      console.error('❌ Failed to fetch stats:', error);
      setStatsLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await Promise.all([fetchUsers(), fetchStats()]);
    setRefreshing(false);
  };

  // User actions
  const handleCall = (user: User) => {
    console.log('📞 Calling user:', user.username);
    // TODO: Implement call functionality
  };

  const handleMessage = (user: User) => {
    console.log('💬 Messaging user:', user.username);
    // TODO: Implement messaging functionality
  };

  const handleViewDetails = (user: User) => {
    console.log('👁️ Viewing details for user:', user.username);
    // TODO: Implement user details modal
  };

  // User management actions
  const handleCreateUser = () => {
    setSelectedUser(null);
    setDialogMode('create');
    setShowUserDialog(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setDialogMode('edit');
    setShowUserDialog(true);
  };

  const handleUserDialogSuccess = () => {
    fetchUsers(); // Refresh users list
  };

  // Bulk operations
  const handleBulkDelete = async (userIds: string[]) => {
    const response = await apiClient.bulkDeleteUsers(userIds);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete users');
    }
    fetchUsers();
    setSelectedUsers([]);
  };

  const handleBulkKick = async (userIds: string[]) => {
    const response = await apiClient.bulkKickUsers(userIds);
    if (!response.success) {
      throw new Error(response.message || 'Failed to kick users');
    }
    fetchUsers();
    setSelectedUsers([]);
  };

  const handleBulkPasswordReset = async (userIds: string[], newPassword: string) => {
    const response = await apiClient.bulkPasswordReset(userIds, newPassword);
    if (!response.success) {
      throw new Error(response.message || 'Failed to reset passwords');
    }
    fetchUsers();
    setSelectedUsers([]);
  };

  const handleImportUsers = async (users: any[]) => {
    const response = await apiClient.importUsers(users);
    if (!response.success) {
      throw new Error(response.message || 'Failed to import users');
    }
    fetchUsers();
  };

  const handleExportUsers = (users: User[]) => {
    const dataStr = JSON.stringify(users, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `opensips-users-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Permissions management
  const handleManagePermissions = (user: User) => {
    setPermissionsUser(user);
    setShowPermissionsDialog(true);
  };

  const handleSavePermissions = async (userId: string, permissions: string[], role?: string) => {
    const response = await apiClient.updateUserPermissions(userId, permissions, role);
    if (!response.success) {
      throw new Error(response.message || 'Failed to update permissions');
    }
    fetchUsers();
  };

  // User selection for bulk operations
  const handleUserSelect = (user: User, selected: boolean) => {
    if (selected) {
      setSelectedUsers(prev => [...prev, user]);
    } else {
      setSelectedUsers(prev => prev.filter(u => u.username !== user.username));
    }
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers);
    }
  };

  useEffect(() => {
    console.log('🔄 Users page mounted');

    // Initial data fetch
    fetchUsers();
    fetchStats();

    // Setup socket connection for real-time updates with enhanced events
    const setupSocket = async () => {
      try {
        await socketService.connect();
        console.log('👥 Setting up real-time users updates...');

        // Main users update handler
        socketService.subscribeToUsers((data: any) => {
          console.log('👥 Real-time users update received:', data);
          if (data.data && data.data.users) {
            console.log('👥 Updating users state with:', data.data.users.length, 'users');
            setUsers(data.data.users);
            setStats(prev => ({
              ...prev,
              total_users: data.data.total || 0,
              online_users: data.data.online || 0,
              offline_users: data.data.offline || 0,
              avg_expires_in: prev?.avg_expires_in || 0,
              unique_user_agents: prev?.unique_user_agents || 0,
              unique_ip_addresses: prev?.unique_ip_addresses || 0
            }));
          }
        });

        // Individual user status updates for granular changes
        socketService.subscribeToUserStatus((data: any) => {
          console.log('👤 User status update:', data);
          setUsers(prevUsers => {
            const updatedUsers = prevUsers.map(user =>
              user.username === data.username && user.domain === data.domain
                ? {
                    ...user,
                    status: data.status,
                    expires_in_seconds: data.expires_in_seconds,
                    last_seen_formatted: data.last_seen,
                    user_agent: data.user_agent,
                    ip_address: data.ip_address
                  }
                : user
            );
            console.log('👤 Updated users after status change:', updatedUsers);
            return updatedUsers;
          });
        });

        // User registration events for notifications
        socketService.subscribeToUserRegistration((data: any) => {
          console.log('📝 User registration event:', data);
          console.log(`✅ User ${data.username} registered with ${data.user_agent}`);
        });

        // User expiration warnings
        socketService.subscribeToUserExpiring((data: any) => {
          console.log('⏰ User expiring warning:', data);
          console.log(`⚠️ User ${data.username} registration expires in ${data.expires_in} seconds`);
        });

        console.log('🔌 All socket event handlers setup complete');

      } catch (error) {
        console.error('❌ Failed to setup socket for users:', error);
      }
    };

    setupSocket();

    // Cleanup on unmount
    return () => {
      socketService.unsubscribeFromUsers();
    };
  }, []);

  // Filter users based on current filter settings
  const filteredUsers = users.filter(user => {
    // Status filter
    if (filter.status !== 'all' && user.status !== filter.status) {
      return false;
    }

    // Search filter
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      const matchesSearch =
        user.username.toLowerCase().includes(searchLower) ||
        (user.email_address && user.email_address.toLowerCase().includes(searchLower)) ||
        user.domain.toLowerCase().includes(searchLower) ||
        (user.ip_address && user.ip_address.toLowerCase().includes(searchLower));

      if (!matchesSearch) return false;
    }

    // Domain filter
    if (filter.domain && filter.domain !== 'all' && user.domain !== filter.domain) {
      return false;
    }

    // User agent filter
    if (filter.userAgent && filter.userAgent !== 'all' && user.user_agent !== filter.userAgent) {
      return false;
    }

    return true;
  });

  // Get unique domains and user agents for filter options
  const uniqueDomains = [...new Set(users.map(u => u.domain))];
  const uniqueUserAgents = [...new Set(users.map(u => u.user_agent).filter(Boolean))];

  const handleFilterChange = (key: keyof UserFilter, value: string) => {
    setFilter(prev => ({ ...prev, [key]: value }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Users className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Đang tải danh sách người dùng...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý người dùng SIP</h1>
          <p className="text-muted-foreground">
            Theo dõi và quản lý người dùng SIP trong thời gian thực
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {hasPermission('users.bulk') && (
            <Button
              variant="outline"
              onClick={() => setShowBulkDialog(true)}
              disabled={selectedUsers.length === 0}
            >
              <Settings className="h-4 w-4 mr-2" />
              Bulk Operations ({selectedUsers.length})
            </Button>
          )}
          {hasPermission('users.write') && (
            <Button onClick={handleCreateUser}>
              <Plus className="h-4 w-4 mr-2" />
              Thêm người dùng
            </Button>
          )}
          <Button
            variant="outline"
            onClick={refreshData}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Làm mới
          </Button>
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            {viewMode === 'grid' ? <List className="h-4 w-4 mr-2" /> : <Grid className="h-4 w-4 mr-2" />}
            {viewMode === 'grid' ? 'Danh sách' : 'Lưới'}
          </Button>
        </div>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="users" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="users" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Người dùng ({filteredUsers.length})
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center">
            <BarChart3 className="h-4 w-4 mr-2" />
            Thống kê
          </TabsTrigger>
        </TabsList>

        <TabsContent value="stats" className="space-y-6">
          <UserStats
            stats={stats ? {
              overview: {
                total_users: stats.total_users,
                online_users: stats.online_users,
                offline_users: stats.offline_users,
                avg_expires_in: stats.avg_expires_in,
                unique_user_agents: stats.unique_user_agents,
                unique_ip_addresses: stats.unique_ip_addresses
              },
              userAgents: [],
              domains: [],
              timestamp: new Date().toISOString()
            } : null}
            loading={statsLoading}
          />
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Bộ lọc
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Tìm kiếm</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm theo tên, email, IP..."
                      value={filter.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Trạng thái</label>
                  <Select value={filter.status} onValueChange={(value) => handleFilterChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      <SelectItem value="online">Online</SelectItem>
                      <SelectItem value="offline">Offline</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Domain</label>
                  <Select value={filter.domain} onValueChange={(value) => handleFilterChange('domain', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn domain" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      {uniqueDomains.map(domain => (
                        <SelectItem key={domain} value={domain}>{domain}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Ứng dụng SIP</label>
                  <Select value={filter.userAgent} onValueChange={(value) => handleFilterChange('userAgent', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn ứng dụng" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      {uniqueUserAgents.filter(ua => ua).map(ua => (
                        <SelectItem key={ua} value={ua!}>{ua}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Display */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <UserCardSkeleton key={i} />
              ))}
            </div>
          ) : filteredUsers.length === 0 ? (
            <Card>
              <CardContent className="p-12">
                <div className="text-center">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Không tìm thấy người dùng</h3>
                  <p className="text-muted-foreground">
                    {users.length === 0
                      ? 'Chưa có người dùng nào trong hệ thống'
                      : 'Thử thay đổi bộ lọc để xem thêm người dùng'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredUsers.map((user) => (
                    <UserCard
                      key={user.username}
                      user={user}
                      onCall={handleCall}
                      onMessage={handleMessage}
                      onViewDetails={handleViewDetails}
                      actions={
                        <UserActions
                          user={user}
                          onEdit={handleEditUser}
                          onRefresh={fetchUsers}
                          onManagePermissions={handleManagePermissions}
                        />
                      }
                    />
                  ))}
                </div>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Danh sách người dùng</CardTitle>
                    <CardDescription>
                      {filteredUsers.length} người dùng được tìm thấy
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {filteredUsers.map((user) => (
                        <div key={user.username} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className={`w-3 h-3 rounded-full ${user.status === 'online' ? 'bg-green-500' : 'bg-gray-400'}`} />
                            <div>
                              <div className="font-medium">{user.username}@{user.domain}</div>
                              <div className="text-sm text-muted-foreground">
                                {user.user_agent || 'Không rõ ứng dụng'} • {user.ip_address || 'Không rõ IP'}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={user.status === 'online' ? 'default' : 'secondary'}>
                              {user.status === 'online' ? 'Online' : 'Offline'}
                            </Badge>
                            {user.status === 'online' && (
                              <Button size="sm" onClick={() => handleCall(user)}>
                                Gọi
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* User Management Dialog */}
      <UserManagementDialog
        open={showUserDialog}
        onOpenChange={setShowUserDialog}
        user={selectedUser}
        mode={dialogMode}
        onSuccess={handleUserDialogSuccess}
      />

      {/* Bulk Operations Dialog */}
      <BulkOperationsDialog
        open={showBulkDialog}
        onOpenChange={setShowBulkDialog}
        selectedUsers={selectedUsers}
        onBulkDelete={handleBulkDelete}
        onBulkKick={handleBulkKick}
        onBulkPasswordReset={handleBulkPasswordReset}
        onImportUsers={handleImportUsers}
        onExportUsers={handleExportUsers}
        allUsers={users}
      />

      {/* Permissions Dialog */}
      {permissionsUser && (
        <PermissionsDialog
          open={showPermissionsDialog}
          onOpenChange={setShowPermissionsDialog}
          user={permissionsUser}
          onSave={handleSavePermissions}
        />
      )}
    </div>
  );
}

export default function UsersPage() {
  return (
    <ProtectedRoute requiredPermission="users.read">
      <UsersPageContent />
    </ProtectedRoute>
  );
}
