'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Phone,
  Plus,
  Search,
  RefreshCw,
  Settings,
  Activity,
  TrendingUp,
  Zap,
  Globe,
  Shield,
  Clock,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  X
} from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { Trunk, Gateway, TrunkStats } from '@/types/trunk';
import { GatewayDialog } from '@/components/trunks/GatewayDialog';
import apiClient from '@/lib/api';

export default function TrunksPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('trunks');
  const [trunks, setTrunks] = useState<Trunk[]>([]);
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [stats, setStats] = useState<TrunkStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showGatewayDialog, setShowGatewayDialog] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<Gateway | null>(null);
  const [gatewayDialogMode, setGatewayDialogMode] = useState<'create' | 'edit'>('create');

  // Mock data for demonstration
  const mockTrunks: Trunk[] = [
    {
      id: 'trunk_1',
      name: 'Primary SIP Trunk',
      description: 'Main SIP trunk for outbound calls',
      type: 'sip',
      gateways: [],
      primaryGateway: 'gw_1',
      failoverEnabled: true,
      dialPattern: '^\\+?[1-9]\\d{7,14}$',
      stripDigits: 0,
      prependDigits: '',
      maxConcurrentCalls: 100,
      currentCalls: 15,
      status: 'active',
      costPerMinute: 0.02,
      currency: 'USD',
      billingIncrement: 60,
      codecPreference: ['G.711', 'G.729'],
      packetization: 20,
      allowedIPs: ['***********/24'],
      authentication: true,
      encryption: false,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z'
    },
    {
      id: 'trunk_2',
      name: 'Backup PSTN Trunk',
      description: 'Backup trunk via PSTN gateway',
      type: 'pstn',
      gateways: [],
      primaryGateway: 'gw_2',
      failoverEnabled: false,
      dialPattern: '^[0-9]{10}$',
      stripDigits: 1,
      prependDigits: '9',
      maxConcurrentCalls: 30,
      currentCalls: 3,
      status: 'active',
      costPerMinute: 0.05,
      currency: 'USD',
      billingIncrement: 60,
      codecPreference: ['G.711'],
      packetization: 20,
      allowedIPs: [],
      authentication: false,
      encryption: false,
      createdAt: '2024-01-10T08:00:00Z',
      updatedAt: '2024-01-18T12:00:00Z'
    }
  ];

  const mockGateways: Gateway[] = [
    {
      id: 'gw_1',
      name: 'SIP Provider Gateway',
      description: 'Primary SIP provider gateway',
      host: 'sip.provider.com',
      port: 5060,
      protocol: 'UDP',
      username: 'user123',
      password: '****',
      realm: 'provider.com',
      register: true,
      registerExpires: 3600,
      keepAlive: true,
      keepAliveInterval: 30,
      maxChannels: 100,
      currentChannels: 15,
      status: 'active',
      lastSeen: '2024-01-20T15:45:00Z',
      totalCalls: 1250,
      successfulCalls: 1180,
      failedCalls: 70,
      averageCallDuration: 180,
      codec: ['G.711', 'G.729'],
      dtmfMode: 'rfc2833',
      callerIdMode: 'pai',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z'
    },
    {
      id: 'gw_2',
      name: 'PSTN Gateway',
      description: 'Local PSTN gateway',
      host: '*************',
      port: 5060,
      protocol: 'UDP',
      register: false,
      keepAlive: true,
      keepAliveInterval: 60,
      maxChannels: 30,
      currentChannels: 3,
      status: 'active',
      lastSeen: '2024-01-20T15:40:00Z',
      totalCalls: 450,
      successfulCalls: 430,
      failedCalls: 20,
      averageCallDuration: 240,
      codec: ['G.711'],
      dtmfMode: 'rfc2833',
      callerIdMode: 'from',
      createdAt: '2024-01-10T08:00:00Z',
      updatedAt: '2024-01-18T12:00:00Z'
    }
  ];

  const mockStats: TrunkStats = {
    totalTrunks: 2,
    activeTrunks: 2,
    totalGateways: 2,
    activeGateways: 2,
    totalCalls: 1700,
    activeCalls: 18,
    callsPerHour: 45,
    successRate: 94.7,
    averageCallDuration: 195,
    totalCapacity: 130,
    usedCapacity: 18,
    capacityUtilization: 13.8,
    totalCost: 156.50,
    costPerCall: 0.092,
    averagePacketLoss: 0.1,
    averageJitter: 15,
    averageRTT: 45
  };

  // Load data
  const loadData = async () => {
    setLoading(true);
    try {
      // Load real data from API
      const [gatewaysResponse, statsResponse] = await Promise.all([
        apiClient.getGateways(),
        apiClient.getTrunkStats()
      ]);

      if (gatewaysResponse.success) {
        setGateways(gatewaysResponse.data || []);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      // For now, use mock trunks until we implement trunk grouping
      setTrunks(mockTrunks);
    } catch (error) {
      console.error('Failed to load trunk data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on search
  const filteredTrunks = trunks.filter(trunk =>
    trunk.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    trunk.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredGateways = gateways.filter(gateway =>
    gateway.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    gateway.host.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'inactive':
        return <XCircle className="h-4 w-4 text-gray-400" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'maintenance':
        return <Settings className="h-4 w-4 text-yellow-600" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'inactive':
        return 'secondary';
      case 'error':
        return 'destructive';
      case 'maintenance':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Gateway management functions
  const handleCreateGateway = () => {
    setSelectedGateway(null);
    setGatewayDialogMode('create');
    setShowGatewayDialog(true);
  };

  const handleEditGateway = (gateway: Gateway) => {
    setSelectedGateway(gateway);
    setGatewayDialogMode('edit');
    setShowGatewayDialog(true);
  };

  const handleSaveGateway = async (gatewayData: any) => {
    try {
      if (gatewayDialogMode === 'create') {
        const response = await apiClient.createGateway(gatewayData);
        if (!response.success) {
          throw new Error(response.message || 'Failed to create gateway');
        }
      } else {
        const response = await apiClient.updateGateway(selectedGateway?.id || '', gatewayData);
        if (!response.success) {
          throw new Error(response.message || 'Failed to update gateway');
        }
      }

      // Reload data
      loadData();
    } catch (error: any) {
      throw error;
    }
  };

  const handleDeleteGateway = async (gateway: Gateway) => {
    if (!confirm(`Are you sure you want to delete gateway "${gateway.name}"?`)) {
      return;
    }

    try {
      const response = await apiClient.deleteGateway(gateway.id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete gateway');
      }

      // Reload data
      loadData();
      alert('Gateway deleted successfully');
    } catch (error: any) {
      alert(error.message || 'Failed to delete gateway');
    }
  };

  const handleTestGateway = async (gateway: Gateway) => {
    try {
      const response = await apiClient.testGateway(gateway.id);
      if (response.success) {
        alert(`Gateway test successful!\nResponse time: ${response.data?.responseTime}ms`);
      } else {
        alert(`Gateway test failed: ${response.message}`);
      }
    } catch (error: any) {
      alert(`Gateway test failed: ${error.message}`);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <ProtectedRoute requiredPermission="trunks.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Trunk Management</h1>
            <p className="text-muted-foreground">Manage SIP trunks, gateways and routing</p>
          </div>
          <div className="flex items-center space-x-2">
            {hasPermission('trunks.write') && (
              <>
                <Button onClick={handleCreateGateway}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Gateway
                </Button>
                <Button variant="outline">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Trunk
                </Button>
              </>
            )}
            <Button variant="outline" onClick={loadData} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search trunks and gateways..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trunks">Trunks ({trunks.length})</TabsTrigger>
            <TabsTrigger value="gateways">Gateways ({gateways.length})</TabsTrigger>
            <TabsTrigger value="routing">Routing</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Capacity Overview */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Capacity Usage</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.usedCapacity}/{stats.totalCapacity}</div>
                    <Progress value={stats.capacityUtilization} className="mt-2" />
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatPercentage(stats.capacityUtilization)} utilization
                    </p>
                  </CardContent>
                </Card>

                {/* Success Rate */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatPercentage(stats.successRate)}</div>
                    <p className="text-xs text-muted-foreground">
                      {stats.totalCalls} total calls
                    </p>
                  </CardContent>
                </Card>

                {/* Cost Per Call */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Cost Per Call</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(stats.costPerCall)}</div>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(stats.totalCost)} total cost
                    </p>
                  </CardContent>
                </Card>

                {/* Active Calls */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
                    <Phone className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.activeCalls}</div>
                    <p className="text-xs text-muted-foreground">
                      {stats.callsPerHour} calls/hour
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="trunks" className="space-y-4">
            <div className="grid gap-4">
              {loading ? (
                <div className="text-center py-8">Loading trunks...</div>
              ) : filteredTrunks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No trunks found
                </div>
              ) : (
                filteredTrunks.map((trunk) => (
                  <Card key={trunk.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg">{trunk.name}</CardTitle>
                          <Badge variant={getStatusVariant(trunk.status)}>
                            {getStatusIcon(trunk.status)}
                            <span className="ml-1">{trunk.status}</span>
                          </Badge>
                          <Badge variant="outline">{trunk.type.toUpperCase()}</Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          {hasPermission('trunks.write') && (
                            <>
                              <Button size="sm" variant="outline">
                                <Settings className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline">
                                Test
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                      {trunk.description && (
                        <CardDescription>{trunk.description}</CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                          <p className="text-sm font-medium">Capacity</p>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold">{trunk.currentCalls}</span>
                            <span className="text-muted-foreground">/ {trunk.maxConcurrentCalls}</span>
                          </div>
                          <Progress 
                            value={(trunk.currentCalls / trunk.maxConcurrentCalls) * 100} 
                            className="mt-1" 
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Cost</p>
                          <p className="text-lg font-bold">
                            {formatCurrency(trunk.costPerMinute || 0)}/min
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Dial Pattern</p>
                          <p className="text-sm font-mono bg-muted px-2 py-1 rounded">
                            {trunk.dialPattern}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Codecs</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {trunk.codecPreference.slice(0, 2).map((codec) => (
                              <Badge key={codec} variant="secondary" className="text-xs">
                                {codec}
                              </Badge>
                            ))}
                            {trunk.codecPreference.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{trunk.codecPreference.length - 2}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="gateways" className="space-y-4">
            <div className="grid gap-4">
              {loading ? (
                <div className="text-center py-8">Loading gateways...</div>
              ) : filteredGateways.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No gateways found
                </div>
              ) : (
                filteredGateways.map((gateway) => (
                  <Card key={gateway.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg">{gateway.name}</CardTitle>
                          <Badge variant={getStatusVariant(gateway.status)}>
                            {getStatusIcon(gateway.status)}
                            <span className="ml-1">{gateway.status}</span>
                          </Badge>
                          <Badge variant="outline">{gateway.protocol}</Badge>
                          {gateway.register && (
                            <Badge variant="secondary">
                              <Shield className="h-3 w-3 mr-1" />
                              Registered
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          {hasPermission('trunks.write') && (
                            <>
                              <Button size="sm" variant="outline" onClick={() => handleEditGateway(gateway)}>
                                <Settings className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline" onClick={() => handleTestGateway(gateway)}>
                                <Zap className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="destructive" onClick={() => handleDeleteGateway(gateway)}>
                                <X className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                      {gateway.description && (
                        <CardDescription>{gateway.description}</CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                          <p className="text-sm font-medium">Endpoint</p>
                          <p className="text-sm font-mono">
                            {gateway.host}:{gateway.port}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Channels</p>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold">{gateway.currentChannels}</span>
                            <span className="text-muted-foreground">/ {gateway.maxChannels}</span>
                          </div>
                          <Progress 
                            value={gateway.maxChannels ? (gateway.currentChannels / gateway.maxChannels) * 100 : 0} 
                            className="mt-1" 
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Success Rate</p>
                          <p className="text-lg font-bold">
                            {((gateway.successfulCalls / gateway.totalCalls) * 100).toFixed(1)}%
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {gateway.totalCalls} total calls
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Last Seen</p>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">
                              {gateway.lastSeen ? new Date(gateway.lastSeen).toLocaleTimeString() : 'Never'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="routing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Routing Rules</CardTitle>
                <CardDescription>
                  Configure how calls are routed through trunks and gateways
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Routing rules management coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Gateway Dialog */}
        <GatewayDialog
          open={showGatewayDialog}
          onOpenChange={setShowGatewayDialog}
          gateway={selectedGateway}
          mode={gatewayDialogMode}
          onSave={handleSaveGateway}
        />
      </div>
    </ProtectedRoute>
  );
}
