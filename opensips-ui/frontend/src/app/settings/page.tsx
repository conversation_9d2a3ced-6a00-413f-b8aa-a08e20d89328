'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Save,
  RotateCcw,
  Download,
  Upload,
  Shield,
  Bell,
  Activity,
  Server,
  Search
} from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';
import { Settings as ApiSettings } from '@/types/api';

interface SettingValue {
  value: any;
  type?: string;
  options?: string[];
  description?: string;
}

export default function SettingsPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<ApiSettings | null>(null);
  const [originalSettings, setOriginalSettings] = useState<ApiSettings | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Load settings
  const loadSettings = async () => {
    if (!hasPermission('settings.read')) return;

    setLoading(true);
    try {
      const response = await apiClient.getSettings();
      if (response.success && response.data && response.data.settings) {
        setSettings(response.data.settings);
        setOriginalSettings(response.data.settings);
        setHasChanges(false);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    if (!hasPermission('settings.write') || !settings) return;

    setSaving(true);
    try {
      const response = await apiClient.updateSettings(settings);
      if (response.success) {
        setHasChanges(false);
        alert('Settings saved successfully!');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Update setting value
  const updateSetting = (category: string, key: string, value: string) => {
    if (!settings) return;

    const newSettings = { ...settings };
    if (!newSettings[category as keyof ApiSettings]) {
      newSettings[category as keyof ApiSettings] = {};
    }

    newSettings[category as keyof ApiSettings][key] = {
      ...newSettings[category as keyof ApiSettings][key],
      value
    };

    setSettings(newSettings);
    setHasChanges(true);
  };

  // Export settings
  const exportSettings = () => {
    if (!settings) return;

    const dataStr = JSON.stringify({
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: settings
    }, null, 2);

    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `opensips-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Import settings
  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target?.result as string);
        if (importedData.settings) {
          setSettings(importedData.settings);
          setHasChanges(true);
          alert('Settings imported successfully!');
        } else {
          alert('Invalid settings file format');
        }
      } catch (error) {
        alert('Failed to parse settings file');
      }
    };
    reader.readAsText(file);

    // Reset input
    event.target.value = '';
  };

  // Reset all settings
  const resetSettings = () => {
    if (!originalSettings) return;

    if (confirm('Are you sure you want to reset all settings to their original values? This will discard all unsaved changes.')) {
      setSettings({ ...originalSettings });
      setHasChanges(false);
    }
  };

  // Validate setting value
  const validateSetting = (type: string, value: string): boolean => {
    switch (type) {
      case 'number':
        return !isNaN(Number(value)) && Number(value) >= 0;
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'boolean':
        return value === 'true' || value === 'false';
      default:
        return value.trim().length > 0;
    }
  };

  // Filter settings based on search term
  const filterSettings = (categorySettings: any) => {
    if (!searchTerm) return categorySettings;

    const filtered: any = {};
    Object.entries(categorySettings).forEach(([key, setting]) => {
      const settingObj = setting as SettingValue;
      if (
        key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        settingObj.description?.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        filtered[key] = setting;
      }
    });
    return filtered;
  };

  // Render setting input
  const renderSettingInput = (category: string, key: string, setting: SettingValue) => {
    const { value, type = 'text', options = [] } = setting;
    const isValid = validateSetting(type, value);

    switch (type) {
      case 'boolean':
        return (
          <Switch
            checked={value === 'true'}
            onCheckedChange={(checked) => updateSetting(category, key, checked.toString())}
          />
        );

      case 'select':
        return (
          <Select value={value} onValueChange={(newValue) => updateSetting(category, key, newValue)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'number':
        return (
          <div className="space-y-1">
            <Input
              type="number"
              value={value}
              onChange={(e) => updateSetting(category, key, e.target.value)}
              className={!isValid ? 'border-red-500' : ''}
            />
            {!isValid && (
              <p className="text-sm text-red-500">Please enter a valid number</p>
            )}
          </div>
        );

      case 'email':
        return (
          <div className="space-y-1">
            <Input
              type="email"
              value={value}
              onChange={(e) => updateSetting(category, key, e.target.value)}
              className={!isValid ? 'border-red-500' : ''}
            />
            {!isValid && (
              <p className="text-sm text-red-500">Please enter a valid email address</p>
            )}
          </div>
        );

      case 'password':
        return (
          <Input
            type="password"
            value={value}
            onChange={(e) => updateSetting(category, key, e.target.value)}
            className={!isValid ? 'border-red-500' : ''}
          />
        );

      default:
        return (
          <div className="space-y-1">
            <Input
              type="text"
              value={value}
              onChange={(e) => updateSetting(category, key, e.target.value)}
              className={!isValid ? 'border-red-500' : ''}
            />
            {!isValid && (
              <p className="text-sm text-red-500">This field is required</p>
            )}
          </div>
        );
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'general': return <Settings className="h-4 w-4" />;
      case 'sip': return <Server className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'notifications': return <Bell className="h-4 w-4" />;
      case 'monitoring': return <Activity className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  // Get category title
  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'general': return 'General Settings';
      case 'sip': return 'SIP Configuration';
      case 'security': return 'Security Settings';
      case 'notifications': return 'Notifications';
      case 'monitoring': return 'Monitoring & Logging';
      default: return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  return (
    <ProtectedRoute requiredPermission="settings.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">System Settings</h1>
            <p className="text-muted-foreground">Configure system preferences and options</p>
          </div>
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <Badge variant="secondary">Unsaved changes</Badge>
            )}
            <Button variant="outline" onClick={exportSettings}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            {hasPermission('settings.write') && (
              <>
                <input
                  type="file"
                  accept=".json"
                  onChange={importSettings}
                  style={{ display: 'none' }}
                  id="import-settings"
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-settings')?.click()}
                  disabled={saving}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </Button>
                <Button
                  variant="outline"
                  onClick={resetSettings}
                  disabled={saving || !hasChanges}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset All
                </Button>
                <Button 
                  onClick={saveSettings}
                  disabled={!hasChanges || saving}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            )}
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">Loading settings...</div>
        ) : settings ? (
          <>
            {/* Search Bar */}
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search settings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              {Object.keys(settings).map((category) => (
                <TabsTrigger key={category} value={category} className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <span className="hidden sm:inline">{getCategoryTitle(category)}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.entries(settings).map(([category, categorySettings]) => (
              <TabsContent key={category} value={category} className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      {getCategoryIcon(category)}
                      <CardTitle>{getCategoryTitle(category)}</CardTitle>
                    </div>
                    <CardDescription>
                      Configure {category} related settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {Object.entries(filterSettings(categorySettings)).map(([key, setting], index) => (
                      <div key={key}>
                        {index > 0 && <Separator className="my-4" />}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                          <div className="space-y-1">
                            <Label htmlFor={`${category}-${key}`} className="font-medium">
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              {(setting as SettingValue).description}
                            </p>
                          </div>
                          <div className="md:col-span-2">
                            {hasPermission('settings.write') ? (
                              renderSettingInput(category, key, setting as SettingValue)
                            ) : (
                              <div className="p-2 bg-muted rounded text-sm">
                                {(setting as SettingValue).type === 'boolean'
                                  ? ((setting as SettingValue).value === 'true' ? 'Enabled' : 'Disabled')
                                  : (setting as SettingValue).value || 'Not set'
                                }
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
          </>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No settings available
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
