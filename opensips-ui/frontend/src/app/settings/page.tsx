'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  Download, 
  Shield,
  Bell,
  Activity,
  Server
} from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';
import { Settings as ApiSettings } from '@/types/api';

interface SettingValue {
  value: any;
  type?: string;
  options?: string[];
  description?: string;
}

export default function SettingsPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<ApiSettings | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Load settings
  const loadSettings = async () => {
    if (!hasPermission('settings.read')) return;
    
    setLoading(true);
    try {
      const response = await apiClient.getSettings();
      if (response.success && response.data) {
        setSettings(response.data);
        setHasChanges(false);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    if (!hasPermission('settings.write') || !settings) return;
    
    setSaving(true);
    try {
      const response = await apiClient.updateSettings(settings);
      if (response.success) {
        setHasChanges(false);
        alert('Settings saved successfully!');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Update setting value
  const updateSetting = (category: string, key: string, value: string) => {
    if (!settings) return;
    
    const newSettings = { ...settings };
    if (!newSettings[category as keyof ApiSettings]) {
      newSettings[category as keyof ApiSettings] = {};
    }
    
    newSettings[category as keyof ApiSettings][key] = {
      ...newSettings[category as keyof ApiSettings][key],
      value
    };
    
    setSettings(newSettings);
    setHasChanges(true);
  };

  // Render setting input
  const renderSettingInput = (category: string, key: string, setting: SettingValue) => {
    const { value, type = 'text', options = [] } = setting;
    
    switch (type) {
      case 'boolean':
        return (
          <Switch
            checked={value === 'true'}
            onCheckedChange={(checked) => updateSetting(category, key, checked.toString())}
          />
        );
      
      case 'select':
        return (
          <Select value={value} onValueChange={(newValue) => updateSetting(category, key, newValue)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => updateSetting(category, key, e.target.value)}
          />
        );
      
      default:
        return (
          <Input
            type="text"
            value={value}
            onChange={(e) => updateSetting(category, key, e.target.value)}
          />
        );
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'general': return <Settings className="h-4 w-4" />;
      case 'sip': return <Server className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'notifications': return <Bell className="h-4 w-4" />;
      case 'monitoring': return <Activity className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  // Get category title
  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'general': return 'General Settings';
      case 'sip': return 'SIP Configuration';
      case 'security': return 'Security Settings';
      case 'notifications': return 'Notifications';
      case 'monitoring': return 'Monitoring & Logging';
      default: return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  return (
    <ProtectedRoute requiredPermission="settings.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">System Settings</h1>
            <p className="text-muted-foreground">Configure system preferences and options</p>
          </div>
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <Badge variant="secondary">Unsaved changes</Badge>
            )}
            <Button variant="outline" onClick={() => alert('Export feature')}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            {hasPermission('settings.write') && (
              <>
                <Button 
                  variant="outline" 
                  onClick={() => alert('Reset feature')}
                  disabled={saving}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset All
                </Button>
                <Button 
                  onClick={saveSettings}
                  disabled={!hasChanges || saving}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            )}
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">Loading settings...</div>
        ) : settings ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              {Object.keys(settings).map((category) => (
                <TabsTrigger key={category} value={category} className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <span className="hidden sm:inline">{getCategoryTitle(category)}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.entries(settings).map(([category, categorySettings]) => (
              <TabsContent key={category} value={category} className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      {getCategoryIcon(category)}
                      <CardTitle>{getCategoryTitle(category)}</CardTitle>
                    </div>
                    <CardDescription>
                      Configure {category} related settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {Object.entries(categorySettings).map(([key, setting], index) => (
                      <div key={key}>
                        {index > 0 && <Separator className="my-4" />}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                          <div className="space-y-1">
                            <Label htmlFor={`${category}-${key}`} className="font-medium">
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              {(setting as SettingValue).description}
                            </p>
                          </div>
                          <div className="md:col-span-2">
                            {hasPermission('settings.write') ? (
                              renderSettingInput(category, key, setting as SettingValue)
                            ) : (
                              <div className="p-2 bg-muted rounded text-sm">
                                {(setting as SettingValue).type === 'boolean'
                                  ? ((setting as SettingValue).value === 'true' ? 'Enabled' : 'Disabled')
                                  : (setting as SettingValue).value || 'Not set'
                                }
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No settings available
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
